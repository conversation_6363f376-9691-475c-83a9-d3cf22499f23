{"name": "vue3-big-event-admin", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vueup/vue-quill": "^1.2.0", "axios": "^1.4.0", "element-plus": "^2.3.7", "pinia": "^2.1.3", "spark-md5": "^3.0.2", "vue": "^3.3.4", "vue-router": "^4.2.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^7.1.0", "eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "husky": "^8.0.0", "lint-staged": "^13.2.3", "pinia-plugin-persistedstate": "^3.1.0", "prettier": "^2.8.8", "sass": "^1.63.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.3.9"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix"]}}