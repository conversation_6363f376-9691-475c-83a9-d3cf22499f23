lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@element-plus/icons-vue':
        specifier: ^2.1.0
        version: https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-2.1.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      '@vueup/vue-quill':
        specifier: ^1.2.0
        version: https://registry.npmmirror.com/@vueup/vue-quill/-/vue-quill-1.2.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      axios:
        specifier: ^1.4.0
        version: https://registry.npmmirror.com/axios/-/axios-1.4.0.tgz
      element-plus:
        specifier: ^2.3.7
        version: https://registry.npmmirror.com/element-plus/-/element-plus-2.3.7.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      pinia:
        specifier: ^2.1.3
        version: https://registry.npmmirror.com/pinia/-/pinia-2.1.3.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      spark-md5:
        specifier: ^3.0.2
        version: 3.0.2
      vue:
        specifier: ^3.3.4
        version: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz
      vue-router:
        specifier: ^4.2.2
        version: https://registry.npmmirror.com/vue-router/-/vue-router-4.2.2.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
    devDependencies:
      '@rushstack/eslint-patch':
        specifier: ^1.2.0
        version: https://registry.npmmirror.com/@rushstack/eslint-patch/-/eslint-patch-1.2.0.tgz
      '@vitejs/plugin-vue':
        specifier: ^4.2.3
        version: https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-4.2.3.tgz(vite@https://registry.npmmirror.com/vite/-/vite-4.3.9.tgz(sass@https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz))(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      '@vue/eslint-config-prettier':
        specifier: ^7.1.0
        version: https://registry.npmmirror.com/@vue/eslint-config-prettier/-/eslint-config-prettier-7.1.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)(prettier@https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz)
      eslint:
        specifier: ^8.39.0
        version: https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz
      eslint-plugin-vue:
        specifier: ^9.11.0
        version: https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-9.11.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)
      husky:
        specifier: ^8.0.0
        version: https://registry.npmmirror.com/husky/-/husky-8.0.0.tgz
      lint-staged:
        specifier: ^13.2.3
        version: https://registry.npmmirror.com/lint-staged/-/lint-staged-13.2.3.tgz
      pinia-plugin-persistedstate:
        specifier: ^3.1.0
        version: https://registry.npmmirror.com/pinia-plugin-persistedstate/-/pinia-plugin-persistedstate-3.1.0.tgz(pinia@https://registry.npmmirror.com/pinia/-/pinia-2.1.3.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz))
      prettier:
        specifier: ^2.8.8
        version: https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz
      sass:
        specifier: ^1.63.6
        version: https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz
      unplugin-auto-import:
        specifier: ^0.16.6
        version: https://registry.npmmirror.com/unplugin-auto-import/-/unplugin-auto-import-0.16.6.tgz(@vueuse/core@9.13.0(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz))(rollup@3.26.2)
      unplugin-vue-components:
        specifier: ^0.25.1
        version: https://registry.npmmirror.com/unplugin-vue-components/-/unplugin-vue-components-0.25.1.tgz(@babel/parser@7.22.7)(rollup@3.26.2)(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      vite:
        specifier: ^4.3.9
        version: https://registry.npmmirror.com/vite/-/vite-4.3.9.tgz(sass@https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz)

packages:

  '@aashutoshrathi/word-wrap@https://registry.npmmirror.com/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz':
    resolution: {integrity: sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA==}
    version: 1.2.6
    engines: {node: '>=0.10.0'}

  '@antfu/utils@https://registry.npmmirror.com/@antfu/utils/-/utils-0.7.5.tgz':
    resolution: {integrity: sha512-dlR6LdS+0SzOAPx/TPRhnoi7hE251OVeT2Snw0RguNbBSbjUHdWr0l3vcUUDg26rEysT89kCbtw1lVorBXLLCg==}
    version: 0.7.5

  '@babel/helper-string-parser@7.22.5':
    resolution: {integrity: sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz':
    resolution: {integrity: sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==}
    version: 7.22.5
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.22.5':
    resolution: {integrity: sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz':
    resolution: {integrity: sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==}
    version: 7.22.5
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.22.7':
    resolution: {integrity: sha512-7NF8pOkHP5o2vpmGgNGcfAeCvOYhGLyA3Z4eBQkT1RJlWu47n63bCs93QfJ2hIAFCil7L5P2IWhs1oToVgrL0Q==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/parser@https://registry.npmmirror.com/@babel/parser/-/parser-7.22.7.tgz':
    resolution: {integrity: sha512-7NF8pOkHP5o2vpmGgNGcfAeCvOYhGLyA3Z4eBQkT1RJlWu47n63bCs93QfJ2hIAFCil7L5P2IWhs1oToVgrL0Q==}
    version: 7.22.7
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/types@7.22.5':
    resolution: {integrity: sha512-zo3MIHGOkPOfoRXitsgHLjEXmlDaD/5KU1Uzuc9GNiZPhSqVxVRtxuPaSBZDsYZ9qV88AjtMtWW7ww98loJ9KA==}
    engines: {node: '>=6.9.0'}

  '@babel/types@https://registry.npmmirror.com/@babel/types/-/types-7.22.5.tgz':
    resolution: {integrity: sha512-zo3MIHGOkPOfoRXitsgHLjEXmlDaD/5KU1Uzuc9GNiZPhSqVxVRtxuPaSBZDsYZ9qV88AjtMtWW7ww98loJ9KA==}
    version: 7.22.5
    engines: {node: '>=6.9.0'}

  '@ctrl/tinycolor@https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.0.tgz':
    resolution: {integrity: sha512-/Z3l6pXthq0JvMYdUFyX9j0MaCltlIn6mfh9jLyQwg5aPKxkyNa0PTHtU1AlFXLNk55ZuAeJRcpvq+tmLfKmaQ==}
    version: 3.6.0
    engines: {node: '>=10'}

  '@element-plus/icons-vue@https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-2.1.0.tgz':
    resolution: {integrity: sha512-PSBn3elNoanENc1vnCfh+3WA9fimRC7n+fWkf3rE5jvv+aBohNHABC/KAR5KWPecxWxDTVT1ERpRbOMRcOV/vA==}
    version: 2.1.0
    peerDependencies:
      vue: ^3.2.0

  '@esbuild/android-arm64@0.17.19':
    resolution: {integrity: sha512-KBMWvEZooR7+kzY0BtbTQn0OAYY7CsiydT63pVEaPtVYF0hXbUaOyZog37DKxK7NF3XacBJOpYT4adIJh+avxA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.17.19':
    resolution: {integrity: sha512-rIKddzqhmav7MSmoFCmDIb6e2W57geRsM94gV2l38fzhXMwq7hZoClug9USI2pFRGL06f4IOPHHpFNOkWieR8A==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.17.19':
    resolution: {integrity: sha512-uUTTc4xGNDT7YSArp/zbtmbhO0uEEK9/ETW29Wk1thYUJBz3IVnvgEiEwEa9IeLyvnpKrWK64Utw2bgUmDveww==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.17.19':
    resolution: {integrity: sha512-80wEoCfF/hFKM6WE1FyBHc9SfUblloAWx6FJkFWTWiCoht9Mc0ARGEM47e67W9rI09YoUxJL68WHfDRYEAvOhg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.17.19':
    resolution: {integrity: sha512-IJM4JJsLhRYr9xdtLytPLSH9k/oxR3boaUIYiHkAawtwNOXKE8KoU8tMvryogdcT8AU+Bflmh81Xn6Q0vTZbQw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.17.19':
    resolution: {integrity: sha512-pBwbc7DufluUeGdjSU5Si+P3SoMF5DQ/F/UmTSb8HXO80ZEAJmrykPyzo1IfNbAoaqw48YRpv8shwd1NoI0jcQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.17.19':
    resolution: {integrity: sha512-4lu+n8Wk0XlajEhbEffdy2xy53dpR06SlzvhGByyg36qJw6Kpfk7cp45DR/62aPH9mtJRmIyrXAS5UWBrJT6TQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.17.19':
    resolution: {integrity: sha512-ct1Tg3WGwd3P+oZYqic+YZF4snNl2bsnMKRkb3ozHmnM0dGWuxcPTTntAF6bOP0Sp4x0PjSF+4uHQ1xvxfRKqg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.17.19':
    resolution: {integrity: sha512-cdmT3KxjlOQ/gZ2cjfrQOtmhG4HJs6hhvm3mWSRDPtZ/lP5oe8FWceS10JaSJC13GBd4eH/haHnqf7hhGNLerA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.17.19':
    resolution: {integrity: sha512-w4IRhSy1VbsNxHRQpeGCHEmibqdTUx61Vc38APcsRbuVgK0OPEnQ0YD39Brymn96mOx48Y2laBQGqgZ0j9w6SQ==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.17.19':
    resolution: {integrity: sha512-2iAngUbBPMq439a+z//gE+9WBldoMp1s5GWsUSgqHLzLJ9WoZLZhpwWuym0u0u/4XmZ3gpHmzV84PonE+9IIdQ==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.17.19':
    resolution: {integrity: sha512-LKJltc4LVdMKHsrFe4MGNPp0hqDFA1Wpt3jE1gEyM3nKUvOiO//9PheZZHfYRfYl6AwdTH4aTcXSqBerX0ml4A==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.17.19':
    resolution: {integrity: sha512-/c/DGybs95WXNS8y3Ti/ytqETiW7EU44MEKuCAcpPto3YjQbyK3IQVKfF6nbghD7EcLUGl0NbiL5Rt5DMhn5tg==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.17.19':
    resolution: {integrity: sha512-FC3nUAWhvFoutlhAkgHf8f5HwFWUL6bYdvLc/TTuxKlvLi3+pPzdZiFKSWz/PF30TB1K19SuCxDTI5KcqASJqA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.17.19':
    resolution: {integrity: sha512-IbFsFbxMWLuKEbH+7sTkKzL6NJmG2vRyy6K7JJo55w+8xDk7RElYn6xvXtDW8HCfoKBFK69f3pgBJSUSQPr+4Q==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.17.19':
    resolution: {integrity: sha512-68ngA9lg2H6zkZcyp22tsVt38mlhWde8l3eJLWkyLrp4HwMUr3c1s/M2t7+kHIhvMjglIBrFpncX1SzMckomGw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.17.19':
    resolution: {integrity: sha512-CwFq42rXCR8TYIjIfpXCbRX0rp1jo6cPIUPSaWwzbVI4aOfX96OXY8M6KNmtPcg7QjYeDmN+DD0Wp3LaBOLf4Q==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.17.19':
    resolution: {integrity: sha512-cnq5brJYrSZ2CF6c35eCmviIN3k3RczmHz8eYaVlNasVqsNY+JKohZU5MKmaOI+KkllCdzOKKdPs762VCPC20g==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.17.19':
    resolution: {integrity: sha512-vCRT7yP3zX+bKWFeP/zdS6SqdWB8OIpaRq/mbXQxTGHnIxspRtigpkUcDMlSCOejlHowLqII7K2JKevwyRP2rg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.17.19':
    resolution: {integrity: sha512-yYx+8jwowUstVdorcMdNlzklLYhPxjniHWFKgRqH7IFlUEa0Umu3KuYplf1HUZZ422e3NU9F4LGb+4O0Kdcaag==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.17.19':
    resolution: {integrity: sha512-eggDKanJszUtCdlVs0RB+h35wNlb5v4TWEkq4vZcmVt5u/HiDZrTXe2bWFQUez3RgNHwx/x4sk5++4NSSicKkw==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.17.19':
    resolution: {integrity: sha512-lAhycmKnVOuRYNtRtatQR1LPQf2oYCkRGkSFnseDAKPl8lu5SOsK/e1sXe5a0Pc5kHIHe6P2I/ilntNv2xf3cA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz':
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    version: 4.4.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.5.1.tgz':
    resolution: {integrity: sha512-Z5ba73P98O1KUYCCJTUeVpja9RcGoMdncZ6T49FCUl2lN38JtCJ+3WgIDBv0AuY4WChU5PmtJmOCTlN6FZTFKQ==}
    version: 4.5.1
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-2.1.0.tgz':
    resolution: {integrity: sha512-Lj7DECXqIVCqnqjjHMPna4vn6GJcMgul/wuS0je9OZ9gsL0zzDpKPVtcG1HaDVc+9y+qgXneTeUMbCqXJNpH1A==}
    version: 2.1.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@https://registry.npmmirror.com/@eslint/js/-/js-8.39.0.tgz':
    resolution: {integrity: sha512-kf9RB0Fg7NZfap83B3QOqOGg9QmD9yBudqQXzzOtn3i4y7ZUXe5ONeW34Gwi+TxhH4mvj72R1Zc300KUMa9Bng==}
    version: 8.39.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@floating-ui/core@https://registry.npmmirror.com/@floating-ui/core/-/core-1.3.1.tgz':
    resolution: {integrity: sha512-Bu+AMaXNjrpjh41znzHqaz3r2Nr8hHuHZT6V2LBKMhyMl0FgKA62PNYbqnfgmzOhoWZj70Zecisbo4H1rotP5g==}
    version: 1.3.1

  '@floating-ui/dom@https://registry.npmmirror.com/@floating-ui/dom/-/dom-1.4.4.tgz':
    resolution: {integrity: sha512-21hhDEPOiWkGp0Ys4Wi6Neriah7HweToKra626CIK712B5m9qkdz54OP9gVldUg+URnBTpv/j/bi/skmGdstXQ==}
    version: 1.4.4

  '@humanwhocodes/config-array@https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.11.10.tgz':
    resolution: {integrity: sha512-KVVjQmNUepDVGXNuoRRdmmEjruj0KfiGSbS8LVc12LMsWDQzRXJ0qdhN8L8uUigKpfEHRhlaQFY0ib1tnUbNeQ==}
    version: 0.11.10
    engines: {node: '>=10.10.0'}

  '@humanwhocodes/module-importer@https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    version: 1.0.1
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz':
    resolution: {integrity: sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==}
    version: 1.2.1

  '@jridgewell/sourcemap-codec@https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz':
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}
    version: 1.4.15

  '@nodelib/fs.scandir@https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    version: 2.1.5
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    version: 2.0.5
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    version: 1.2.8
    engines: {node: '>= 8'}

  '@rollup/pluginutils@https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-5.0.2.tgz':
    resolution: {integrity: sha512-pTd9rIsP92h+B6wWwFbW8RkZv4hiR/xKsqre4SIuAOaOEQRxi0lqLke9k2/7WegC85GgUs9pjmOjCUi3In4vwA==}
    version: 5.0.2
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rushstack/eslint-patch@https://registry.npmmirror.com/@rushstack/eslint-patch/-/eslint-patch-1.2.0.tgz':
    resolution: {integrity: sha512-sXo/qW2/pAcmT43VoRKOJbDOfV3cYpq3szSVfIThQXNt+E4DfKj361vaAt3c88U5tPUxzEswam7GW48PJqtKAg==}
    version: 1.2.0

  '@sxzz/popperjs-es@2.11.7':
    resolution: {integrity: sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==}

  '@types/estree@https://registry.npmmirror.com/@types/estree/-/estree-1.0.1.tgz':
    resolution: {integrity: sha512-LG4opVs2ANWZ1TJoKc937iMmNstM/d0ae1vNbnBvBhqCSezgVUOzcLCqbI5elV8Vy6WKwKjaqR+zO9VKirBBCA==}
    version: 1.0.1

  '@types/lodash-es@https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.7.tgz':
    resolution: {integrity: sha512-z0ptr6UI10VlU6l5MYhGwS4mC8DZyYer2mCoyysZtSF7p26zOX8UpbrV0YpNYLGS8K4PUFIyEr62IMFFjveSiQ==}
    version: 4.17.7

  '@types/lodash@https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.195.tgz':
    resolution: {integrity: sha512-Hwx9EUgdwf2GLarOjQp5ZH8ZmblzcbTBC2wtQWNKARBSxM9ezRIAUpeDTgoQRAFB0+8CNWXVA9+MaSOzOF3nPg==}
    version: 4.14.195

  '@types/web-bluetooth@0.0.16':
    resolution: {integrity: sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==}

  '@types/web-bluetooth@https://registry.npmmirror.com/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz':
    resolution: {integrity: sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==}
    version: 0.0.16

  '@vitejs/plugin-vue@https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-4.2.3.tgz':
    resolution: {integrity: sha512-R6JDUfiZbJA9cMiguQ7jxALsgiprjBeHL5ikpXfJCH62pPHtI+JdJ5xWj6Ev73yXSlYl86+blXn1kZHQ7uElxw==}
    version: 4.2.3
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.0.0
      vue: ^3.2.25

  '@vue/compiler-core@https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.3.4.tgz':
    resolution: {integrity: sha512-cquyDNvZ6jTbf/+x+AgM2Arrp6G4Dzbb0R64jiG804HRMfRiFXWI6kqUVqZ6ZR0bQhIoQjB4+2bhNtVwndW15g==}
    version: 3.3.4

  '@vue/compiler-dom@https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.3.4.tgz':
    resolution: {integrity: sha512-wyM+OjOVpuUukIq6p5+nwHYtj9cFroz9cwkfmP9O1nzH68BenTTv0u7/ndggT8cIQlnBeOo6sUT/gvHcIkLA5w==}
    version: 3.3.4

  '@vue/compiler-sfc@https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.3.4.tgz':
    resolution: {integrity: sha512-6y/d8uw+5TkCuzBkgLS0v3lSM3hJDntFEiUORM11pQ/hKvkhSKZrXW6i69UyXlJQisJxuUEJKAWEqWbWsLeNKQ==}
    version: 3.3.4

  '@vue/compiler-ssr@https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.3.4.tgz':
    resolution: {integrity: sha512-m0v6oKpup2nMSehwA6Uuu+j+wEwcy7QmwMkVNVfrV9P2qE5KshC6RwOCq8fjGS/Eak/uNb8AaWekfiXxbBB6gQ==}
    version: 3.3.4

  '@vue/devtools-api@https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.5.0.tgz':
    resolution: {integrity: sha512-o9KfBeaBmCKl10usN4crU53fYtC1r7jJwdGKjPT24t348rHxgfpZ0xL3Xm/gLUYnc0oTp8LAmrxOeLyu6tbk2Q==}
    version: 6.5.0

  '@vue/eslint-config-prettier@https://registry.npmmirror.com/@vue/eslint-config-prettier/-/eslint-config-prettier-7.1.0.tgz':
    resolution: {integrity: sha512-Pv/lVr0bAzSIHLd9iz0KnvAr4GKyCEl+h52bc4e5yWuDVtLgFwycF7nrbWTAQAS+FU6q1geVd07lc6EWfJiWKQ==}
    version: 7.1.0
    peerDependencies:
      eslint: '>= 7.28.0'
      prettier: '>= 2.0.0'

  '@vue/reactivity-transform@https://registry.npmmirror.com/@vue/reactivity-transform/-/reactivity-transform-3.3.4.tgz':
    resolution: {integrity: sha512-MXgwjako4nu5WFLAjpBnCj/ieqcjE2aJBINUNQzkZQfzIZA4xn+0fV1tIYBJvvva3N3OvKGofRLvQIwEQPpaXw==}
    version: 3.3.4

  '@vue/reactivity@https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.3.4.tgz':
    resolution: {integrity: sha512-kLTDLwd0B1jG08NBF3R5rqULtv/f8x3rOFByTDz4J53ttIQEDmALqKqXY0J+XQeN0aV2FBxY8nJDf88yvOPAqQ==}
    version: 3.3.4

  '@vue/runtime-core@https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.3.4.tgz':
    resolution: {integrity: sha512-R+bqxMN6pWO7zGI4OMlmvePOdP2c93GsHFM/siJI7O2nxFRzj55pLwkpCedEY+bTMgp5miZ8CxfIZo3S+gFqvA==}
    version: 3.3.4

  '@vue/runtime-dom@https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.3.4.tgz':
    resolution: {integrity: sha512-Aj5bTJ3u5sFsUckRghsNjVTtxZQ1OyMWCr5dZRAPijF/0Vy4xEoRCwLyHXcj4D0UFbJ4lbx3gPTgg06K/GnPnQ==}
    version: 3.3.4

  '@vue/server-renderer@https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.3.4.tgz':
    resolution: {integrity: sha512-Q6jDDzR23ViIb67v+vM1Dqntu+HUexQcsWKhhQa4ARVzxOY2HbC7QRW/ggkDBd5BU+uM1sV6XOAP0b216o34JQ==}
    version: 3.3.4
    peerDependencies:
      vue: 3.3.4

  '@vue/shared@https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz':
    resolution: {integrity: sha512-7OjdcV8vQ74eiz1TZLzZP4JwqM5fA94K6yntPS5Z25r9HDuGNzaGdgvwKYq6S+MxwF0TFRwe50fIR/MYnakdkQ==}
    version: 3.3.4

  '@vueup/vue-quill@https://registry.npmmirror.com/@vueup/vue-quill/-/vue-quill-1.2.0.tgz':
    resolution: {integrity: sha512-kd5QPSHMDpycklojPXno2Kw2JSiKMYduKYQckTm1RJoVDA557MnyUXgcuuDpry4HY/Rny9nGNcK+m3AHk94wag==}
    version: 1.2.0
    peerDependencies:
      vue: ^3.2.41

  '@vueuse/core@9.13.0':
    resolution: {integrity: sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==}
    version: 9.13.0

  '@vueuse/core@https://registry.npmmirror.com/@vueuse/core/-/core-9.13.0.tgz':
    resolution: {integrity: sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==}
    version: 9.13.0

  '@vueuse/metadata@9.13.0':
    resolution: {integrity: sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==}

  '@vueuse/metadata@https://registry.npmmirror.com/@vueuse/metadata/-/metadata-9.13.0.tgz':
    resolution: {integrity: sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==}
    version: 9.13.0

  '@vueuse/shared@9.13.0':
    resolution: {integrity: sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==}
    version: 9.13.0

  '@vueuse/shared@https://registry.npmmirror.com/@vueuse/shared/-/shared-9.13.0.tgz':
    resolution: {integrity: sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==}
    version: 9.13.0

  acorn-jsx@https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    version: 5.3.2
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz:
    resolution: {integrity: sha512-F0SAmZ8iUtS//m8DmCTA0jlh6TDKkHQyK6xc6V4KDTyZKA9dnvX9/3sRTVQrWm79glUAZbnmmNcdYwUIHWVybw==}
    version: 8.10.0
    engines: {node: '>=0.4.0'}
    hasBin: true

  aggregate-error@https://registry.npmmirror.com/aggregate-error/-/aggregate-error-3.1.0.tgz:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    version: 3.1.0
    engines: {node: '>=8'}

  ajv@https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    version: 6.12.6

  ansi-escapes@https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    version: 4.3.2
    engines: {node: '>=8'}

  ansi-regex@https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    version: 5.0.1
    engines: {node: '>=8'}

  ansi-regex@https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.0.1.tgz:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==}
    version: 6.0.1
    engines: {node: '>=12'}

  ansi-styles@https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    version: 4.3.0
    engines: {node: '>=8'}

  ansi-styles@https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    version: 6.2.1
    engines: {node: '>=12'}

  anymatch@https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    version: 3.1.3
    engines: {node: '>= 8'}

  argparse@https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    version: 2.0.1

  astral-regex@https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    version: 2.0.0
    engines: {node: '>=8'}

  async-validator@https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz:
    resolution: {integrity: sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==}
    version: 4.2.5

  asynckit@https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}
    version: 0.4.0

  axios@https://registry.npmmirror.com/axios/-/axios-1.4.0.tgz:
    resolution: {integrity: sha512-S4XCWMEmzvo64T9GfvQDOXgYRDJ/wsSZc7Jvdgx5u1sd0JwsuPLqb3SYmusag+edF6ziyMensPVqLTSc1PiSEA==}
    version: 1.4.0

  balanced-match@https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}
    version: 1.0.2

  binary-extensions@https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    version: 2.2.0
    engines: {node: '>=8'}

  boolbase@https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}
    version: 1.0.0

  brace-expansion@https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    version: 1.1.11

  brace-expansion@https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    version: 2.0.1

  braces@https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    version: 3.0.2
    engines: {node: '>=8'}

  call-bind@https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz:
    resolution: {integrity: sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==}
    version: 1.0.2

  callsites@https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    version: 3.1.0
    engines: {node: '>=6'}

  chalk@https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    version: 4.1.2
    engines: {node: '>=10'}

  chalk@https://registry.npmmirror.com/chalk/-/chalk-5.2.0.tgz:
    resolution: {integrity: sha512-ree3Gqw/nazQAPuJJEy+avdl7QfZMcUvmHIKgEZkGL+xOBzRvup5Hxo6LHuMceSxOabuJLJm5Yp/92R9eMmMvA==}
    version: 5.2.0
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  chokidar@https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    version: 3.5.3
    engines: {node: '>= 8.10.0'}

  clean-stack@https://registry.npmmirror.com/clean-stack/-/clean-stack-2.2.0.tgz:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    version: 2.2.0
    engines: {node: '>=6'}

  cli-cursor@https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    version: 3.1.0
    engines: {node: '>=8'}

  cli-truncate@https://registry.npmmirror.com/cli-truncate/-/cli-truncate-2.1.0.tgz:
    resolution: {integrity: sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg==}
    version: 2.1.0
    engines: {node: '>=8'}

  cli-truncate@https://registry.npmmirror.com/cli-truncate/-/cli-truncate-3.1.0.tgz:
    resolution: {integrity: sha512-wfOBkjXteqSnI59oPcJkcPl/ZmwvMMOj340qUIY1SKZCv0B9Cf4D4fAucRkIKQmsIuYK3x1rrgU7MeGRruiuiA==}
    version: 3.1.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  clone@https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz:
    resolution: {integrity: sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==}
    version: 2.1.2
    engines: {node: '>=0.8'}

  color-convert@https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    version: 2.0.1
    engines: {node: '>=7.0.0'}

  color-name@https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}
    version: 1.1.4

  colorette@https://registry.npmmirror.com/colorette/-/colorette-2.0.20.tgz:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}
    version: 2.0.20

  combined-stream@https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    version: 1.0.8
    engines: {node: '>= 0.8'}

  commander@https://registry.npmmirror.com/commander/-/commander-10.0.1.tgz:
    resolution: {integrity: sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==}
    version: 10.0.1
    engines: {node: '>=14'}

  concat-map@https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}
    version: 0.0.1

  cross-spawn@https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    version: 7.0.3
    engines: {node: '>= 8'}

  cssesc@https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    version: 3.0.0
    engines: {node: '>=4'}
    hasBin: true

  csstype@https://registry.npmmirror.com/csstype/-/csstype-3.1.2.tgz:
    resolution: {integrity: sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==}
    version: 3.1.2

  dayjs@https://registry.npmmirror.com/dayjs/-/dayjs-1.11.9.tgz:
    resolution: {integrity: sha512-QvzAURSbQ0pKdIye2txOzNaHmxtUBXerpY0FJsFXUMKbIZeFm5ht1LS/jFsrncjnmtv8HsG0W2g6c0zUjZWmpA==}
    version: 1.11.9

  debug@https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    version: 4.3.4
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  deep-equal@https://registry.npmmirror.com/deep-equal/-/deep-equal-1.1.1.tgz:
    resolution: {integrity: sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==}
    version: 1.1.1

  deep-is@https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}
    version: 0.1.4

  define-properties@https://registry.npmmirror.com/define-properties/-/define-properties-1.2.0.tgz:
    resolution: {integrity: sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==}
    version: 1.2.0
    engines: {node: '>= 0.4'}

  delayed-stream@https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    version: 1.0.0
    engines: {node: '>=0.4.0'}

  doctrine@https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    version: 3.0.0
    engines: {node: '>=6.0.0'}

  eastasianwidth@https://registry.npmmirror.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}
    version: 0.2.0

  element-plus@https://registry.npmmirror.com/element-plus/-/element-plus-2.3.7.tgz:
    resolution: {integrity: sha512-h6TxclbaLUJxg/Bv5j/ZKsK+K5yadQliw5+R30HWyE69pXlqXTX24oYx+yw3pA4Dy+lqEDi5501FQ0CORk3OSA==}
    version: 2.3.7
    peerDependencies:
      vue: ^3.2.0

  emoji-regex@https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    version: 8.0.0

  emoji-regex@https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}
    version: 9.2.2

  esbuild@https://registry.npmmirror.com/esbuild/-/esbuild-0.17.19.tgz:
    resolution: {integrity: sha512-XQ0jAPFkK/u3LcVRcvVHQcTIqD6E2H1fvZMA5dQPSOWb3suUbWbfbRf94pjc0bNzRYLfIrDRQXr7X+LHIm5oHw==}
    version: 0.17.19
    engines: {node: '>=12'}
    hasBin: true

  escape-html@https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}
    version: 1.0.3

  escape-string-regexp@https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    version: 4.0.0
    engines: {node: '>=10'}

  escape-string-regexp@https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    version: 5.0.0
    engines: {node: '>=12'}

  eslint-config-prettier@https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.8.0.tgz:
    resolution: {integrity: sha512-wLbQiFre3tdGgpDv67NQKnJuTlcUVYHas3k+DZCc2U2BadthoEY4B7hLPvAxaqdyOGCzuLfii2fqGph10va7oA==}
    version: 8.8.0
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-prettier@https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz:
    resolution: {integrity: sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==}
    version: 4.2.1
    engines: {node: '>=12.0.0'}
    peerDependencies:
      eslint: '>=7.28.0'
      eslint-config-prettier: '*'
      prettier: '>=2.0.0'
    peerDependenciesMeta:
      eslint-config-prettier:
        optional: true

  eslint-plugin-vue@https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-9.11.0.tgz:
    resolution: {integrity: sha512-bBCJAZnkBV7ATH4Z1E7CvN3nmtS4H7QUU3UBxPdo8WohRU+yHjnQRALpTbxMVcz0e4Mx3IyxIdP5HYODMxK9cQ==}
    version: 9.11.0
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.2.0 || ^7.0.0 || ^8.0.0

  eslint-scope@https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.0.tgz:
    resolution: {integrity: sha512-DYj5deGlHBfMt15J7rdtyKNq/Nqlv5KfU4iodrQ019XESsRnwXH9KAE0y3cwtUHDo2ob7CypAnCqefh6vioWRw==}
    version: 7.2.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz:
    resolution: {integrity: sha512-pZnmmLwYzf+kWaM/Qgrvpen51upAktaaiI01nsJD/Yr3lMOdNtq0cxkrrg16w64VtisN6okbs7Q8AfGqj4c9fA==}
    version: 3.4.1
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz:
    resolution: {integrity: sha512-mwiok6cy7KTW7rBpo05k6+p4YVZByLNjAZ/ACB9DRCu4YDRwjXI01tWHp6KAUWelsBetTxKK/2sHB0vdS8Z2Og==}
    version: 8.39.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true

  espree@https://registry.npmmirror.com/espree/-/espree-9.6.0.tgz:
    resolution: {integrity: sha512-1FH/IiruXZ84tpUlm0aCUEwMl2Ho5ilqVh0VvQXw+byAz/4SAciyHLlfmL5WYqsvD38oymdUwBss0LtK8m4s/A==}
    version: 9.6.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esquery@https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz:
    resolution: {integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==}
    version: 1.5.0
    engines: {node: '>=0.10'}

  esrecurse@https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    version: 4.3.0
    engines: {node: '>=4.0'}

  estraverse@https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    version: 5.3.0
    engines: {node: '>=4.0'}

  estree-walker@https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}
    version: 2.0.2

  esutils@https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    version: 2.0.3
    engines: {node: '>=0.10.0'}

  eventemitter3@https://registry.npmmirror.com/eventemitter3/-/eventemitter3-2.0.3.tgz:
    resolution: {integrity: sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==}
    version: 2.0.3

  execa@https://registry.npmmirror.com/execa/-/execa-7.1.1.tgz:
    resolution: {integrity: sha512-wH0eMf/UXckdUYnO21+HDztteVv05rq2GXksxT4fCGeHkBhw1DROXh40wcjMcRqDOWE7iPJ4n3M7e2+YFP+76Q==}
    version: 7.1.1
    engines: {node: ^14.18.0 || ^16.14.0 || >=18.0.0}

  extend@https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}
    version: 3.0.2

  fast-deep-equal@https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}
    version: 3.1.3

  fast-diff@https://registry.npmmirror.com/fast-diff/-/fast-diff-1.1.2.tgz:
    resolution: {integrity: sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig==}
    version: 1.1.2

  fast-diff@https://registry.npmmirror.com/fast-diff/-/fast-diff-1.2.0.tgz:
    resolution: {integrity: sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w==}
    version: 1.2.0

  fast-diff@https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==}
    version: 1.3.0

  fast-glob@https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.0.tgz:
    resolution: {integrity: sha512-ChDuvbOypPuNjO8yIDf36x7BlZX1smcUMTTcyoIjycexOxd6DFsKsg21qVBzEmr3G7fUKIRy2/psii+CIUt7FA==}
    version: 3.3.0
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    version: 2.1.0

  fast-levenshtein@https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}
    version: 2.0.6

  fastq@https://registry.npmmirror.com/fastq/-/fastq-1.15.0.tgz:
    resolution: {integrity: sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==}
    version: 1.15.0

  file-entry-cache@https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    version: 6.0.1
    engines: {node: ^10.12.0 || >=12.0.0}

  fill-range@https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    version: 7.0.1
    engines: {node: '>=8'}

  find-up@https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    version: 5.0.0
    engines: {node: '>=10'}

  flat-cache@https://registry.npmmirror.com/flat-cache/-/flat-cache-3.0.4.tgz:
    resolution: {integrity: sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==}
    version: 3.0.4
    engines: {node: ^10.12.0 || >=12.0.0}

  flatted@https://registry.npmmirror.com/flatted/-/flatted-3.2.7.tgz:
    resolution: {integrity: sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==}
    version: 3.2.7

  follow-redirects@https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.2.tgz:
    resolution: {integrity: sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==}
    version: 1.15.2
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data@https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    version: 4.0.0
    engines: {node: '>= 6'}

  fs.realpath@https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}
    version: 1.0.0

  fsevents@2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}
    version: 1.1.1

  functions-have-names@https://registry.npmmirror.com/functions-have-names/-/functions-have-names-1.2.3.tgz:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    version: 1.2.3

  get-intrinsic@https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.1.tgz:
    resolution: {integrity: sha512-2DcsyfABl+gVHEfCOaTrWgyt+tb6MSEGmKq+kI5HwLbIYgjgmMcV8KQ41uaKz1xxUcn9tJtgFbQUEVcEbd0FYw==}
    version: 1.2.1

  get-stream@https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    version: 6.0.1
    engines: {node: '>=10'}

  glob-parent@https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    version: 5.1.2
    engines: {node: '>= 6'}

  glob-parent@https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    version: 6.0.2
    engines: {node: '>=10.13.0'}

  glob@https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    version: 7.2.3

  globals@https://registry.npmmirror.com/globals/-/globals-13.20.0.tgz:
    resolution: {integrity: sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ==}
    version: 13.20.0
    engines: {node: '>=8'}

  grapheme-splitter@https://registry.npmmirror.com/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz:
    resolution: {integrity: sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==}
    version: 1.0.4

  has-flag@https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    version: 4.0.0
    engines: {node: '>=8'}

  has-property-descriptors@https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz:
    resolution: {integrity: sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==}
    version: 1.0.0

  has-proto@https://registry.npmmirror.com/has-proto/-/has-proto-1.0.1.tgz:
    resolution: {integrity: sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==}
    version: 1.0.1
    engines: {node: '>= 0.4'}

  has-symbols@https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    version: 1.0.3
    engines: {node: '>= 0.4'}

  has-tostringtag@https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    version: 1.0.0
    engines: {node: '>= 0.4'}

  has@https://registry.npmmirror.com/has/-/has-1.0.3.tgz:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    version: 1.0.3
    engines: {node: '>= 0.4.0'}

  human-signals@https://registry.npmmirror.com/human-signals/-/human-signals-4.3.1.tgz:
    resolution: {integrity: sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==}
    version: 4.3.1
    engines: {node: '>=14.18.0'}

  husky@https://registry.npmmirror.com/husky/-/husky-8.0.0.tgz:
    resolution: {integrity: sha512-4qbE/5dzNDNxFEkX9MNRPKl5+omTXQzdILCUWiqG/lWIAioiM5vln265/l6I2Zx8gpW8l1ukZwGQeCFbBZ6+6w==}
    version: 8.0.0
    engines: {node: '>=14'}
    hasBin: true

  ignore@https://registry.npmmirror.com/ignore/-/ignore-5.2.4.tgz:
    resolution: {integrity: sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==}
    version: 5.2.4
    engines: {node: '>= 4'}

  immutable@https://registry.npmmirror.com/immutable/-/immutable-4.3.0.tgz:
    resolution: {integrity: sha512-0AOCmOip+xgJwEVTQj1EfiDDOkPmuyllDuTuEX+DDXUgapLAsBIfkg3sxCYyCEA8mQqZrrxPUGjcOQ2JS3WLkg==}
    version: 4.3.0

  import-fresh@https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    version: 3.3.0
    engines: {node: '>=6'}

  imurmurhash@https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    version: 0.1.4
    engines: {node: '>=0.8.19'}

  indent-string@https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    version: 4.0.0
    engines: {node: '>=8'}

  inflight@https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    version: 1.0.6

  inherits@https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}
    version: 2.0.4

  is-arguments@https://registry.npmmirror.com/is-arguments/-/is-arguments-1.1.1.tgz:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    version: 1.1.1
    engines: {node: '>= 0.4'}

  is-binary-path@https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    version: 2.1.0
    engines: {node: '>=8'}

  is-core-module@https://registry.npmmirror.com/is-core-module/-/is-core-module-2.12.1.tgz:
    resolution: {integrity: sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg==}
    version: 2.12.1

  is-date-object@https://registry.npmmirror.com/is-date-object/-/is-date-object-1.0.5.tgz:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    version: 1.0.5
    engines: {node: '>= 0.4'}

  is-extglob@https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    version: 2.1.1
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    version: 3.0.0
    engines: {node: '>=8'}

  is-fullwidth-code-point@https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz:
    resolution: {integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==}
    version: 4.0.0
    engines: {node: '>=12'}

  is-glob@https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    version: 4.0.3
    engines: {node: '>=0.10.0'}

  is-number@https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    version: 7.0.0
    engines: {node: '>=0.12.0'}

  is-path-inside@https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    version: 3.0.3
    engines: {node: '>=8'}

  is-regex@https://registry.npmmirror.com/is-regex/-/is-regex-1.1.4.tgz:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    version: 1.1.4
    engines: {node: '>= 0.4'}

  is-stream@https://registry.npmmirror.com/is-stream/-/is-stream-3.0.0.tgz:
    resolution: {integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==}
    version: 3.0.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  isexe@https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}
    version: 2.0.0

  js-sdsl@https://registry.npmmirror.com/js-sdsl/-/js-sdsl-4.4.1.tgz:
    resolution: {integrity: sha512-6Gsx8R0RucyePbWqPssR8DyfuXmLBooYN5cZFZKjHGnQuaf7pEzhtpceagJxVu4LqhYY5EYA7nko3FmeHZ1KbA==}
    version: 4.4.1

  js-yaml@https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    version: 4.1.0
    hasBin: true

  json-schema-traverse@https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    version: 0.4.1

  json-stable-stringify-without-jsonify@https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}
    version: 1.0.1

  jsonc-parser@https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.2.0.tgz:
    resolution: {integrity: sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w==}
    version: 3.2.0

  levn@https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    version: 0.4.1
    engines: {node: '>= 0.8.0'}

  lilconfig@https://registry.npmmirror.com/lilconfig/-/lilconfig-2.1.0.tgz:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    version: 2.1.0
    engines: {node: '>=10'}

  lint-staged@https://registry.npmmirror.com/lint-staged/-/lint-staged-13.2.3.tgz:
    resolution: {integrity: sha512-zVVEXLuQIhr1Y7R7YAWx4TZLdvuzk7DnmrsTNL0fax6Z3jrpFcas+vKbzxhhvp6TA55m1SQuWkpzI1qbfDZbAg==}
    version: 13.2.3
    engines: {node: ^14.13.1 || >=16.0.0}
    hasBin: true

  listr2@https://registry.npmmirror.com/listr2/-/listr2-5.0.8.tgz:
    resolution: {integrity: sha512-mC73LitKHj9w6v30nLNGPetZIlfpUniNSsxxrbaPcWOjDb92SHPzJPi/t+v1YC/lxKz/AJ9egOjww0qUuFxBpA==}
    version: 5.0.8
    engines: {node: ^14.13.1 || >=16.0.0}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true

  local-pkg@https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.3.tgz:
    resolution: {integrity: sha512-SFppqq5p42fe2qcZQqqEOiVRXl+WCP1MdT6k7BDEW1j++sp5fIY+/fdRQitvKgB5BrBcmrs5m/L0v2FrU5MY1g==}
    version: 0.4.3
    engines: {node: '>=14'}

  locate-path@https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    version: 6.0.0
    engines: {node: '>=10'}

  lodash-es@https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}
    version: 4.17.21

  lodash-unified@https://registry.npmmirror.com/lodash-unified/-/lodash-unified-1.0.3.tgz:
    resolution: {integrity: sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==}
    version: 1.0.3
    peerDependencies:
      '@types/lodash-es': '*'
      lodash: '*'
      lodash-es: '*'

  lodash.clonedeep@https://registry.npmmirror.com/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz:
    resolution: {integrity: sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==}
    version: 4.5.0

  lodash.isequal@https://registry.npmmirror.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz:
    resolution: {integrity: sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==}
    version: 4.5.0

  lodash.merge@https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}
    version: 4.6.2

  lodash@https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    version: 4.17.21

  log-update@https://registry.npmmirror.com/log-update/-/log-update-4.0.0.tgz:
    resolution: {integrity: sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg==}
    version: 4.0.0
    engines: {node: '>=10'}

  lru-cache@https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    version: 6.0.0
    engines: {node: '>=10'}

  magic-string@https://registry.npmmirror.com/magic-string/-/magic-string-0.30.1.tgz:
    resolution: {integrity: sha512-mbVKXPmS0z0G4XqFDCTllmDQ6coZzn94aMlb0o/A4HEHJCKcanlDZwYJgwnkmgD3jyWhUgj9VsPrfd972yPffA==}
    version: 0.30.1
    engines: {node: '>=12'}

  memoize-one@https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz:
    resolution: {integrity: sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==}
    version: 6.0.0

  merge-stream@https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    version: 2.0.0

  merge2@https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    version: 1.4.1
    engines: {node: '>= 8'}

  micromatch@https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    version: 4.0.5
    engines: {node: '>=8.6'}

  mime-db@https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    version: 1.52.0
    engines: {node: '>= 0.6'}

  mime-types@https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    version: 2.1.35
    engines: {node: '>= 0.6'}

  mimic-fn@https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    version: 2.1.0
    engines: {node: '>=6'}

  mimic-fn@https://registry.npmmirror.com/mimic-fn/-/mimic-fn-4.0.0.tgz:
    resolution: {integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==}
    version: 4.0.0
    engines: {node: '>=12'}

  minimatch@https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    version: 3.1.2

  minimatch@https://registry.npmmirror.com/minimatch/-/minimatch-9.0.3.tgz:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    version: 9.0.3
    engines: {node: '>=16 || 14 >=14.17'}

  mlly@https://registry.npmmirror.com/mlly/-/mlly-1.4.0.tgz:
    resolution: {integrity: sha512-ua8PAThnTwpprIaU47EPeZ/bPUVp2QYBbWMphUQpVdBI3Lgqzm5KZQ45Agm3YJedHXaIHl6pBGabaLSUPPSptg==}
    version: 1.4.0

  ms@https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    version: 2.1.2

  nanoid@https://registry.npmmirror.com/nanoid/-/nanoid-3.3.6.tgz:
    resolution: {integrity: sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==}
    version: 3.3.6
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}
    version: 1.4.0

  normalize-path@https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    version: 3.0.0
    engines: {node: '>=0.10.0'}

  normalize-wheel-es@https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz:
    resolution: {integrity: sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==}
    version: 1.2.0

  npm-run-path@https://registry.npmmirror.com/npm-run-path/-/npm-run-path-5.1.0.tgz:
    resolution: {integrity: sha512-sJOdmRGrY2sjNTRMbSvluQqg+8X7ZK61yvzBEIDhz4f8z1TZFYABsqjjCBd/0PUNE9M6QDgHJXQkGUEm7Q+l9Q==}
    version: 5.1.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  nth-check@https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    version: 2.1.1

  object-inspect@https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.3.tgz:
    resolution: {integrity: sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==}
    version: 1.12.3

  object-is@https://registry.npmmirror.com/object-is/-/object-is-1.1.5.tgz:
    resolution: {integrity: sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==}
    version: 1.1.5
    engines: {node: '>= 0.4'}

  object-keys@https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    version: 1.1.1
    engines: {node: '>= 0.4'}

  once@https://registry.npmmirror.com/once/-/once-1.4.0.tgz:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    version: 1.4.0

  onetime@https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    version: 5.1.2
    engines: {node: '>=6'}

  onetime@https://registry.npmmirror.com/onetime/-/onetime-6.0.0.tgz:
    resolution: {integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==}
    version: 6.0.0
    engines: {node: '>=12'}

  optionator@https://registry.npmmirror.com/optionator/-/optionator-0.9.3.tgz:
    resolution: {integrity: sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==}
    version: 0.9.3
    engines: {node: '>= 0.8.0'}

  p-limit@https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    version: 3.1.0
    engines: {node: '>=10'}

  p-locate@https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    version: 5.0.0
    engines: {node: '>=10'}

  p-map@https://registry.npmmirror.com/p-map/-/p-map-4.0.0.tgz:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    version: 4.0.0
    engines: {node: '>=10'}

  parchment@https://registry.npmmirror.com/parchment/-/parchment-1.1.4.tgz:
    resolution: {integrity: sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg==}
    version: 1.1.4

  parent-module@https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    version: 1.0.1
    engines: {node: '>=6'}

  path-exists@https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    version: 4.0.0
    engines: {node: '>=8'}

  path-is-absolute@https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    version: 1.0.1
    engines: {node: '>=0.10.0'}

  path-key@https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    version: 3.1.1
    engines: {node: '>=8'}

  path-key@https://registry.npmmirror.com/path-key/-/path-key-4.0.0.tgz:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==}
    version: 4.0.0
    engines: {node: '>=12'}

  path-parse@https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    version: 1.0.7

  pathe@https://registry.npmmirror.com/pathe/-/pathe-1.1.1.tgz:
    resolution: {integrity: sha512-d+RQGp0MAYTIaDBIMmOfMwz3E+LOZnxx1HZd5R18mmCZY0QBlK0LDZfPc8FW8Ed2DlvsuE6PRjroDY+wg4+j/Q==}
    version: 1.1.1

  picocolors@https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}
    version: 1.0.0

  picomatch@https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    version: 2.3.1
    engines: {node: '>=8.6'}

  pidtree@https://registry.npmmirror.com/pidtree/-/pidtree-0.6.0.tgz:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==}
    version: 0.6.0
    engines: {node: '>=0.10'}
    hasBin: true

  pinia-plugin-persistedstate@https://registry.npmmirror.com/pinia-plugin-persistedstate/-/pinia-plugin-persistedstate-3.1.0.tgz:
    resolution: {integrity: sha512-8UN+vYMEPBdgNLwceY08mi5olI0wkYaEb8b6hD6xW7SnBRuPydWHlEhZvUWgNb/ibuf4PvufpvtS+dmhYjJQOw==}
    version: 3.1.0
    peerDependencies:
      pinia: ^2.0.0

  pinia@https://registry.npmmirror.com/pinia/-/pinia-2.1.3.tgz:
    resolution: {integrity: sha512-XNA/z/ye4P5rU1pieVmh0g/hSuDO98/a5UC8oSP0DNdvt6YtetJNHTrXwpwsQuflkGT34qKxAEcp7lSxXNjf/A==}
    version: 2.1.3
    peerDependencies:
      '@vue/composition-api': ^1.4.0
      typescript: '>=4.4.4'
      vue: ^2.6.14 || ^3.3.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
      typescript:
        optional: true

  pkg-types@https://registry.npmmirror.com/pkg-types/-/pkg-types-1.0.3.tgz:
    resolution: {integrity: sha512-nN7pYi0AQqJnoLPC9eHFQ8AcyaixBUOwvqc5TDnIKCMEE6I0y8P7OKA7fPexsXGCGxQDl/cmrLAp26LhcwxZ4A==}
    version: 1.0.3

  postcss-selector-parser@https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz:
    resolution: {integrity: sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ==}
    version: 6.0.13
    engines: {node: '>=4'}

  postcss@https://registry.npmmirror.com/postcss/-/postcss-8.4.25.tgz:
    resolution: {integrity: sha512-7taJ/8t2av0Z+sQEvNzCkpDynl0tX3uJMCODi6nT3PfASC7dYCWV9aQ+uiCf+KBD4SEFcu+GvJdGdwzQ6OSjCw==}
    version: 8.4.25
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    version: 1.2.1
    engines: {node: '>= 0.8.0'}

  prettier-linter-helpers@https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==}
    version: 1.0.0
    engines: {node: '>=6.0.0'}

  prettier@https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    version: 2.8.8
    engines: {node: '>=10.13.0'}
    hasBin: true

  proxy-from-env@https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}
    version: 1.1.0

  punycode@https://registry.npmmirror.com/punycode/-/punycode-2.3.0.tgz:
    resolution: {integrity: sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==}
    version: 2.3.0
    engines: {node: '>=6'}

  queue-microtask@https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    version: 1.2.3

  quill-delta@https://registry.npmmirror.com/quill-delta/-/quill-delta-3.6.3.tgz:
    resolution: {integrity: sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==}
    version: 3.6.3
    engines: {node: '>=0.10'}

  quill-delta@https://registry.npmmirror.com/quill-delta/-/quill-delta-4.2.2.tgz:
    resolution: {integrity: sha512-qjbn82b/yJzOjstBgkhtBjN2TNK+ZHP/BgUQO+j6bRhWQQdmj2lH6hXG7+nwwLF41Xgn//7/83lxs9n2BkTtTg==}
    version: 4.2.2

  quill@https://registry.npmmirror.com/quill/-/quill-1.3.7.tgz:
    resolution: {integrity: sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g==}
    version: 1.3.7

  readdirp@https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    version: 3.6.0
    engines: {node: '>=8.10.0'}

  regexp.prototype.flags@https://registry.npmmirror.com/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz:
    resolution: {integrity: sha512-0SutC3pNudRKgquxGoRGIz946MZVHqbNfPjBdxeOhBrdgDKlRoXmYLQN9xRbrR09ZXWeGAdPuif7egofn6v5LA==}
    version: 1.5.0
    engines: {node: '>= 0.4'}

  resolve-from@https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    version: 4.0.0
    engines: {node: '>=4'}

  resolve@https://registry.npmmirror.com/resolve/-/resolve-1.22.2.tgz:
    resolution: {integrity: sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g==}
    version: 1.22.2
    hasBin: true

  restore-cursor@https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    version: 3.1.0
    engines: {node: '>=8'}

  reusify@https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    version: 1.0.4
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@https://registry.npmmirror.com/rfdc/-/rfdc-1.3.0.tgz:
    resolution: {integrity: sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA==}
    version: 1.3.0

  rimraf@https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    version: 3.0.2
    hasBin: true

  rollup@3.26.2:
    resolution: {integrity: sha512-6umBIGVz93er97pMgQO08LuH3m6PUb3jlDUUGFsNJB6VgTCUaDFpupf5JfU30529m/UKOgmiX+uY6Sx8cOYpLA==}
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    hasBin: true

  rollup@https://registry.npmmirror.com/rollup/-/rollup-3.26.2.tgz:
    resolution: {integrity: sha512-6umBIGVz93er97pMgQO08LuH3m6PUb3jlDUUGFsNJB6VgTCUaDFpupf5JfU30529m/UKOgmiX+uY6Sx8cOYpLA==}
    version: 3.26.2
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    hasBin: true

  run-parallel@https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    version: 1.2.0

  rxjs@https://registry.npmmirror.com/rxjs/-/rxjs-7.8.1.tgz:
    resolution: {integrity: sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==}
    version: 7.8.1

  sass@https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz:
    resolution: {integrity: sha512-MJuxGMHzaOW7ipp+1KdELtqKbfAWbH7OLIdoSMnVe3EXPMTmxTmlaZDCTsgIpPCs3w99lLo9/zDKkOrJuT5byw==}
    version: 1.63.6
    engines: {node: '>=14.0.0'}
    hasBin: true

  scule@https://registry.npmmirror.com/scule/-/scule-1.0.0.tgz:
    resolution: {integrity: sha512-4AsO/FrViE/iDNEPaAQlb77tf0csuq27EsVpy6ett584EcRTp6pTDLoGWVxCD77y5iU5FauOvhsI4o1APwPoSQ==}
    version: 1.0.0

  semver@https://registry.npmmirror.com/semver/-/semver-7.5.4.tgz:
    resolution: {integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==}
    version: 7.5.4
    engines: {node: '>=10'}
    hasBin: true

  shebang-command@https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    version: 2.0.0
    engines: {node: '>=8'}

  shebang-regex@https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    version: 3.0.0
    engines: {node: '>=8'}

  signal-exit@https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    version: 3.0.7

  slice-ansi@https://registry.npmmirror.com/slice-ansi/-/slice-ansi-3.0.0.tgz:
    resolution: {integrity: sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==}
    version: 3.0.0
    engines: {node: '>=8'}

  slice-ansi@https://registry.npmmirror.com/slice-ansi/-/slice-ansi-4.0.0.tgz:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    version: 4.0.0
    engines: {node: '>=10'}

  slice-ansi@https://registry.npmmirror.com/slice-ansi/-/slice-ansi-5.0.0.tgz:
    resolution: {integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==}
    version: 5.0.0
    engines: {node: '>=12'}

  source-map-js@https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    version: 1.0.2
    engines: {node: '>=0.10.0'}

  spark-md5@3.0.2:
    resolution: {integrity: sha512-wcFzz9cDfbuqe0FZzfi2or1sgyIrsDwmPwfZC4hiNidPdPINjeUwNfv5kldczoEAcjl9Y1L3SM7Uz2PUEQzxQw==}

  string-argv@https://registry.npmmirror.com/string-argv/-/string-argv-0.3.2.tgz:
    resolution: {integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==}
    version: 0.3.2
    engines: {node: '>=0.6.19'}

  string-width@https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    version: 4.2.3
    engines: {node: '>=8'}

  string-width@https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    version: 5.1.2
    engines: {node: '>=12'}

  strip-ansi@https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    version: 6.0.1
    engines: {node: '>=8'}

  strip-ansi@https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.1.0.tgz:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    version: 7.1.0
    engines: {node: '>=12'}

  strip-final-newline@https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-3.0.0.tgz:
    resolution: {integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==}
    version: 3.0.0
    engines: {node: '>=12'}

  strip-json-comments@https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    version: 3.1.1
    engines: {node: '>=8'}

  strip-literal@https://registry.npmmirror.com/strip-literal/-/strip-literal-1.0.1.tgz:
    resolution: {integrity: sha512-QZTsipNpa2Ppr6v1AmJHESqJ3Uz247MUS0OjrnnZjFAvEoWqxuyFuXn2xLgMtRnijJShAa1HL0gtJyUs7u7n3Q==}
    version: 1.0.1

  supports-color@https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    version: 7.2.0
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    version: 1.0.0
    engines: {node: '>= 0.4'}

  text-table@https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}
    version: 0.2.0

  through@https://registry.npmmirror.com/through/-/through-2.3.8.tgz:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}
    version: 2.3.8

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-fast-properties@https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    version: 2.0.0
    engines: {node: '>=4'}

  to-regex-range@https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    version: 5.0.1
    engines: {node: '>=8.0'}

  tslib@https://registry.npmmirror.com/tslib/-/tslib-2.6.0.tgz:
    resolution: {integrity: sha512-7At1WUettjcSRHXCyYtTselblcHl9PJFFVKiCAy/bY97+BPZXSQ2wbq0P9s8tK2G7dFQfNnlJnPAiArVBVBsfA==}
    version: 2.6.0

  type-check@https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    version: 0.4.0
    engines: {node: '>= 0.8.0'}

  type-fest@https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    version: 0.20.2
    engines: {node: '>=10'}

  type-fest@https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    version: 0.21.3
    engines: {node: '>=10'}

  ufo@https://registry.npmmirror.com/ufo/-/ufo-1.1.2.tgz:
    resolution: {integrity: sha512-TrY6DsjTQQgyS3E3dBaOXf0TpPD8u9FVrVYmKVegJuFw51n/YB9XPt+U6ydzFG5ZIN7+DIjPbNmXoBj9esYhgQ==}
    version: 1.1.2

  unimport@https://registry.npmmirror.com/unimport/-/unimport-3.0.14.tgz:
    resolution: {integrity: sha512-67Rh/sGpEuVqdHWkXaZ6NOq+I7sKt86o+DUtKeGB6dh4Hk1A8AQrzyVGg2+LaVEYotStH7HwvV9YSaRjyT7Uqg==}
    version: 3.0.14

  unplugin-auto-import@https://registry.npmmirror.com/unplugin-auto-import/-/unplugin-auto-import-0.16.6.tgz:
    resolution: {integrity: sha512-M+YIITkx3C/Hg38hp8HmswP5mShUUyJOzpifv7RTlAbeFlO2Tyw0pwrogSSxnipHDPTtI8VHFBpkYkNKzYSuyA==}
    version: 0.16.6
    engines: {node: '>=14'}
    peerDependencies:
      '@nuxt/kit': ^3.2.2
      '@vueuse/core': '*'
    peerDependenciesMeta:
      '@nuxt/kit':
        optional: true
      '@vueuse/core':
        optional: true

  unplugin-vue-components@https://registry.npmmirror.com/unplugin-vue-components/-/unplugin-vue-components-0.25.1.tgz:
    resolution: {integrity: sha512-kzS2ZHVMaGU2XEO2keYQcMjNZkanDSGDdY96uQT9EPe+wqSZwwgbFfKVJ5ti0+8rGAcKHColwKUvctBhq2LJ3A==}
    version: 0.25.1
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/parser': ^7.15.8
      '@nuxt/kit': ^3.2.2
      vue: 2 || 3
    peerDependenciesMeta:
      '@babel/parser':
        optional: true
      '@nuxt/kit':
        optional: true

  unplugin@https://registry.npmmirror.com/unplugin/-/unplugin-1.3.2.tgz:
    resolution: {integrity: sha512-Lh7/2SryjXe/IyWqx9K7IKwuKhuOFZEhotiBquOODsv2IVyDkI9lv/XhgfjdXf/xdbv32txmnBNnC/JVTDJlsA==}
    version: 1.3.2

  uri-js@https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    version: 4.4.1

  util-deprecate@https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}
    version: 1.0.2

  vite@https://registry.npmmirror.com/vite/-/vite-4.3.9.tgz:
    resolution: {integrity: sha512-qsTNZjO9NoJNW7KnOrgYwczm0WctJ8m/yqYAMAK9Lxt4SoySUfS5S8ia9K7JHpa3KEeMfyF8LoJ3c5NeBJy6pg==}
    version: 4.3.9
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vue-demi@0.14.5:
    resolution: {integrity: sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==}
    version: 0.14.5
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-demi@https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.5.tgz:
    resolution: {integrity: sha512-o9NUVpl/YlsGJ7t+xuqJKx8EBGf1quRhCiT6D/J0pfwmk9zUwYkC7yrF4SZCe6fETvSM3UNL2edcbYrSyc4QHA==}
    version: 0.14.5
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-eslint-parser@https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.3.1.tgz:
    resolution: {integrity: sha512-Clr85iD2XFZ3lJ52/ppmUDG/spxQu6+MAeHXjjyI4I1NUYZ9xmenQp4N0oaHJhrA8OOxltCVxMRfANGa70vU0g==}
    version: 9.3.1
    engines: {node: ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'

  vue-router@https://registry.npmmirror.com/vue-router/-/vue-router-4.2.2.tgz:
    resolution: {integrity: sha512-cChBPPmAflgBGmy3tBsjeoe3f3VOSG6naKyY5pjtrqLGbNEXdzCigFUHgBvp9e3ysAtFtEx7OLqcSDh/1Cq2TQ==}
    version: 4.2.2
    peerDependencies:
      vue: ^3.2.0

  vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz:
    resolution: {integrity: sha512-VTyEYn3yvIeY1Py0WaYGZsXnz3y5UnGi62GjVEqvEGPl6nxbOrCXbVOTQWBEJUqAyTUk2uJ5JLVnYJ6ZzGbrSw==}
    version: 3.3.4

  webpack-sources@https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    version: 3.2.3
    engines: {node: '>=10.13.0'}

  webpack-virtual-modules@https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.5.0.tgz:
    resolution: {integrity: sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw==}
    version: 0.5.0

  which@https://registry.npmmirror.com/which/-/which-2.0.2.tgz:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    version: 2.0.2
    engines: {node: '>= 8'}
    hasBin: true

  wrap-ansi@https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-6.2.0.tgz:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    version: 6.2.0
    engines: {node: '>=8'}

  wrap-ansi@https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    version: 7.0.0
    engines: {node: '>=10'}

  wrappy@https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}
    version: 1.0.2

  xml-name-validator@https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz:
    resolution: {integrity: sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==}
    version: 4.0.0
    engines: {node: '>=12'}

  yallist@https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    version: 4.0.0

  yaml@https://registry.npmmirror.com/yaml/-/yaml-2.3.1.tgz:
    resolution: {integrity: sha512-2eHWfjaoXgTBC2jNM1LRef62VQa0umtvRiDSk6HSzW7RvS5YtkabJrwYLLEKWBc8a5U2PTSCs+dJjUTJdlHsWQ==}
    version: 2.3.1
    engines: {node: '>= 14'}

  yocto-queue@https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    version: 0.1.0
    engines: {node: '>=10'}

snapshots:

  '@aashutoshrathi/word-wrap@https://registry.npmmirror.com/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz': {}

  '@antfu/utils@https://registry.npmmirror.com/@antfu/utils/-/utils-0.7.5.tgz': {}

  '@babel/helper-string-parser@7.22.5':
    optional: true

  '@babel/helper-string-parser@https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz': {}

  '@babel/helper-validator-identifier@7.22.5':
    optional: true

  '@babel/helper-validator-identifier@https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz': {}

  '@babel/parser@7.22.7':
    dependencies:
      '@babel/types': 7.22.5
    optional: true

  '@babel/parser@https://registry.npmmirror.com/@babel/parser/-/parser-7.22.7.tgz':
    dependencies:
      '@babel/types': https://registry.npmmirror.com/@babel/types/-/types-7.22.5.tgz

  '@babel/types@7.22.5':
    dependencies:
      '@babel/helper-string-parser': 7.22.5
      '@babel/helper-validator-identifier': 7.22.5
      to-fast-properties: 2.0.0
    optional: true

  '@babel/types@https://registry.npmmirror.com/@babel/types/-/types-7.22.5.tgz':
    dependencies:
      '@babel/helper-string-parser': https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz
      '@babel/helper-validator-identifier': https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz
      to-fast-properties: https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz

  '@ctrl/tinycolor@https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.0.tgz': {}

  '@element-plus/icons-vue@https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-2.1.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)':
    dependencies:
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz

  '@esbuild/android-arm64@0.17.19':
    optional: true

  '@esbuild/android-arm@0.17.19':
    optional: true

  '@esbuild/android-x64@0.17.19':
    optional: true

  '@esbuild/darwin-arm64@0.17.19':
    optional: true

  '@esbuild/darwin-x64@0.17.19':
    optional: true

  '@esbuild/freebsd-arm64@0.17.19':
    optional: true

  '@esbuild/freebsd-x64@0.17.19':
    optional: true

  '@esbuild/linux-arm64@0.17.19':
    optional: true

  '@esbuild/linux-arm@0.17.19':
    optional: true

  '@esbuild/linux-ia32@0.17.19':
    optional: true

  '@esbuild/linux-loong64@0.17.19':
    optional: true

  '@esbuild/linux-mips64el@0.17.19':
    optional: true

  '@esbuild/linux-ppc64@0.17.19':
    optional: true

  '@esbuild/linux-riscv64@0.17.19':
    optional: true

  '@esbuild/linux-s390x@0.17.19':
    optional: true

  '@esbuild/linux-x64@0.17.19':
    optional: true

  '@esbuild/netbsd-x64@0.17.19':
    optional: true

  '@esbuild/openbsd-x64@0.17.19':
    optional: true

  '@esbuild/sunos-x64@0.17.19':
    optional: true

  '@esbuild/win32-arm64@0.17.19':
    optional: true

  '@esbuild/win32-ia32@0.17.19':
    optional: true

  '@esbuild/win32-x64@0.17.19':
    optional: true

  '@eslint-community/eslint-utils@https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)':
    dependencies:
      eslint: https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz
      eslint-visitor-keys: https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz

  '@eslint-community/regexpp@https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.5.1.tgz': {}

  '@eslint/eslintrc@https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-2.1.0.tgz':
    dependencies:
      ajv: https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz
      debug: https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz
      espree: https://registry.npmmirror.com/espree/-/espree-9.6.0.tgz
      globals: https://registry.npmmirror.com/globals/-/globals-13.20.0.tgz
      ignore: https://registry.npmmirror.com/ignore/-/ignore-5.2.4.tgz
      import-fresh: https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz
      js-yaml: https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz
      minimatch: https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz
      strip-json-comments: https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@https://registry.npmmirror.com/@eslint/js/-/js-8.39.0.tgz': {}

  '@floating-ui/core@https://registry.npmmirror.com/@floating-ui/core/-/core-1.3.1.tgz': {}

  '@floating-ui/dom@https://registry.npmmirror.com/@floating-ui/dom/-/dom-1.4.4.tgz':
    dependencies:
      '@floating-ui/core': https://registry.npmmirror.com/@floating-ui/core/-/core-1.3.1.tgz

  '@humanwhocodes/config-array@https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.11.10.tgz':
    dependencies:
      '@humanwhocodes/object-schema': https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz
      debug: https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz
      minimatch: https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz': {}

  '@humanwhocodes/object-schema@https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz': {}

  '@jridgewell/sourcemap-codec@https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz': {}

  '@nodelib/fs.scandir@https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz':
    dependencies:
      '@nodelib/fs.stat': https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz
      run-parallel: https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz

  '@nodelib/fs.stat@https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz': {}

  '@nodelib/fs.walk@https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz':
    dependencies:
      '@nodelib/fs.scandir': https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz
      fastq: https://registry.npmmirror.com/fastq/-/fastq-1.15.0.tgz

  '@rollup/pluginutils@https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-5.0.2.tgz(rollup@3.26.2)':
    dependencies:
      '@types/estree': https://registry.npmmirror.com/@types/estree/-/estree-1.0.1.tgz
      estree-walker: https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz
      picomatch: https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz
    optionalDependencies:
      rollup: 3.26.2

  '@rushstack/eslint-patch@https://registry.npmmirror.com/@rushstack/eslint-patch/-/eslint-patch-1.2.0.tgz': {}

  '@sxzz/popperjs-es@2.11.7': {}

  '@types/estree@https://registry.npmmirror.com/@types/estree/-/estree-1.0.1.tgz': {}

  '@types/lodash-es@https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.7.tgz':
    dependencies:
      '@types/lodash': https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.195.tgz

  '@types/lodash@https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.195.tgz': {}

  '@types/web-bluetooth@0.0.16':
    optional: true

  '@types/web-bluetooth@https://registry.npmmirror.com/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz': {}

  '@vitejs/plugin-vue@https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-4.2.3.tgz(vite@https://registry.npmmirror.com/vite/-/vite-4.3.9.tgz(sass@https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz))(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)':
    dependencies:
      vite: https://registry.npmmirror.com/vite/-/vite-4.3.9.tgz(sass@https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz)
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz

  '@vue/compiler-core@https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.3.4.tgz':
    dependencies:
      '@babel/parser': https://registry.npmmirror.com/@babel/parser/-/parser-7.22.7.tgz
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz
      estree-walker: https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz
      source-map-js: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz

  '@vue/compiler-dom@https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.3.4.tgz':
    dependencies:
      '@vue/compiler-core': https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.3.4.tgz
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz

  '@vue/compiler-sfc@https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.3.4.tgz':
    dependencies:
      '@babel/parser': https://registry.npmmirror.com/@babel/parser/-/parser-7.22.7.tgz
      '@vue/compiler-core': https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.3.4.tgz
      '@vue/compiler-dom': https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.3.4.tgz
      '@vue/compiler-ssr': https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.3.4.tgz
      '@vue/reactivity-transform': https://registry.npmmirror.com/@vue/reactivity-transform/-/reactivity-transform-3.3.4.tgz
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz
      estree-walker: https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz
      magic-string: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.1.tgz
      postcss: https://registry.npmmirror.com/postcss/-/postcss-8.4.25.tgz
      source-map-js: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz

  '@vue/compiler-ssr@https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.3.4.tgz':
    dependencies:
      '@vue/compiler-dom': https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.3.4.tgz
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz

  '@vue/devtools-api@https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.5.0.tgz': {}

  '@vue/eslint-config-prettier@https://registry.npmmirror.com/@vue/eslint-config-prettier/-/eslint-config-prettier-7.1.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)(prettier@https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz)':
    dependencies:
      eslint: https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz
      eslint-config-prettier: https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.8.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)
      eslint-plugin-prettier: https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz(eslint-config-prettier@https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.8.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz))(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)(prettier@https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz)
      prettier: https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz

  '@vue/reactivity-transform@https://registry.npmmirror.com/@vue/reactivity-transform/-/reactivity-transform-3.3.4.tgz':
    dependencies:
      '@babel/parser': https://registry.npmmirror.com/@babel/parser/-/parser-7.22.7.tgz
      '@vue/compiler-core': https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.3.4.tgz
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz
      estree-walker: https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz
      magic-string: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.1.tgz

  '@vue/reactivity@https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.3.4.tgz':
    dependencies:
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz

  '@vue/runtime-core@https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.3.4.tgz':
    dependencies:
      '@vue/reactivity': https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.3.4.tgz
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz

  '@vue/runtime-dom@https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.3.4.tgz':
    dependencies:
      '@vue/runtime-core': https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.3.4.tgz
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz
      csstype: https://registry.npmmirror.com/csstype/-/csstype-3.1.2.tgz

  '@vue/server-renderer@https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.3.4.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)':
    dependencies:
      '@vue/compiler-ssr': https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.3.4.tgz
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz

  '@vue/shared@https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz': {}

  '@vueup/vue-quill@https://registry.npmmirror.com/@vueup/vue-quill/-/vue-quill-1.2.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)':
    dependencies:
      quill: https://registry.npmmirror.com/quill/-/quill-1.3.7.tgz
      quill-delta: https://registry.npmmirror.com/quill-delta/-/quill-delta-4.2.2.tgz
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz

  '@vueuse/core@9.13.0(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)':
    dependencies:
      '@types/web-bluetooth': 0.0.16
      '@vueuse/metadata': 9.13.0
      '@vueuse/shared': 9.13.0(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      vue-demi: 0.14.5(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue
    optional: true

  '@vueuse/core@https://registry.npmmirror.com/@vueuse/core/-/core-9.13.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)':
    dependencies:
      '@types/web-bluetooth': https://registry.npmmirror.com/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz
      '@vueuse/metadata': https://registry.npmmirror.com/@vueuse/metadata/-/metadata-9.13.0.tgz
      '@vueuse/shared': https://registry.npmmirror.com/@vueuse/shared/-/shared-9.13.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      vue-demi: https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.5.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/metadata@9.13.0':
    optional: true

  '@vueuse/metadata@https://registry.npmmirror.com/@vueuse/metadata/-/metadata-9.13.0.tgz': {}

  '@vueuse/shared@9.13.0(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)':
    dependencies:
      vue-demi: 0.14.5(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue
    optional: true

  '@vueuse/shared@https://registry.npmmirror.com/@vueuse/shared/-/shared-9.13.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)':
    dependencies:
      vue-demi: https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.5.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  acorn-jsx@https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz(acorn@https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz):
    dependencies:
      acorn: https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz

  acorn@https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz: {}

  aggregate-error@https://registry.npmmirror.com/aggregate-error/-/aggregate-error-3.1.0.tgz:
    dependencies:
      clean-stack: https://registry.npmmirror.com/clean-stack/-/clean-stack-2.2.0.tgz
      indent-string: https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz

  ajv@https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz:
    dependencies:
      fast-deep-equal: https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz
      fast-json-stable-stringify: https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz
      json-schema-traverse: https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz
      uri-js: https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz

  ansi-escapes@https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz:
    dependencies:
      type-fest: https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz

  ansi-regex@https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz: {}

  ansi-regex@https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.0.1.tgz: {}

  ansi-styles@https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz:
    dependencies:
      color-convert: https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz

  ansi-styles@https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz: {}

  anymatch@https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz:
    dependencies:
      normalize-path: https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz
      picomatch: https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz

  argparse@https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz: {}

  astral-regex@https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz: {}

  async-validator@https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz: {}

  asynckit@https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz: {}

  axios@https://registry.npmmirror.com/axios/-/axios-1.4.0.tgz:
    dependencies:
      follow-redirects: https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.2.tgz
      form-data: https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz
      proxy-from-env: https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz
    transitivePeerDependencies:
      - debug

  balanced-match@https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz: {}

  binary-extensions@https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz: {}

  boolbase@https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz: {}

  brace-expansion@https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz:
    dependencies:
      balanced-match: https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz
      concat-map: https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz

  brace-expansion@https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz:
    dependencies:
      balanced-match: https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz

  braces@https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz:
    dependencies:
      fill-range: https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz

  call-bind@https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz:
    dependencies:
      function-bind: https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz
      get-intrinsic: https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.1.tgz

  callsites@https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz: {}

  chalk@https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz:
    dependencies:
      ansi-styles: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz
      supports-color: https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz

  chalk@https://registry.npmmirror.com/chalk/-/chalk-5.2.0.tgz: {}

  chokidar@https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz:
    dependencies:
      anymatch: https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz
      braces: https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz
      glob-parent: https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz
      is-binary-path: https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz
      is-glob: https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz
      normalize-path: https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz
      readdirp: https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz
    optionalDependencies:
      fsevents: 2.3.2

  clean-stack@https://registry.npmmirror.com/clean-stack/-/clean-stack-2.2.0.tgz: {}

  cli-cursor@https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz:
    dependencies:
      restore-cursor: https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz

  cli-truncate@https://registry.npmmirror.com/cli-truncate/-/cli-truncate-2.1.0.tgz:
    dependencies:
      slice-ansi: https://registry.npmmirror.com/slice-ansi/-/slice-ansi-3.0.0.tgz
      string-width: https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz

  cli-truncate@https://registry.npmmirror.com/cli-truncate/-/cli-truncate-3.1.0.tgz:
    dependencies:
      slice-ansi: https://registry.npmmirror.com/slice-ansi/-/slice-ansi-5.0.0.tgz
      string-width: https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz

  clone@https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz: {}

  color-convert@https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz:
    dependencies:
      color-name: https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz

  color-name@https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz: {}

  colorette@https://registry.npmmirror.com/colorette/-/colorette-2.0.20.tgz: {}

  combined-stream@https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz:
    dependencies:
      delayed-stream: https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz

  commander@https://registry.npmmirror.com/commander/-/commander-10.0.1.tgz: {}

  concat-map@https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz: {}

  cross-spawn@https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz:
    dependencies:
      path-key: https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz
      shebang-command: https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz
      which: https://registry.npmmirror.com/which/-/which-2.0.2.tgz

  cssesc@https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz: {}

  csstype@https://registry.npmmirror.com/csstype/-/csstype-3.1.2.tgz: {}

  dayjs@https://registry.npmmirror.com/dayjs/-/dayjs-1.11.9.tgz: {}

  debug@https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz:
    dependencies:
      ms: https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz

  deep-equal@https://registry.npmmirror.com/deep-equal/-/deep-equal-1.1.1.tgz:
    dependencies:
      is-arguments: https://registry.npmmirror.com/is-arguments/-/is-arguments-1.1.1.tgz
      is-date-object: https://registry.npmmirror.com/is-date-object/-/is-date-object-1.0.5.tgz
      is-regex: https://registry.npmmirror.com/is-regex/-/is-regex-1.1.4.tgz
      object-is: https://registry.npmmirror.com/object-is/-/object-is-1.1.5.tgz
      object-keys: https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz
      regexp.prototype.flags: https://registry.npmmirror.com/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz

  deep-is@https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz: {}

  define-properties@https://registry.npmmirror.com/define-properties/-/define-properties-1.2.0.tgz:
    dependencies:
      has-property-descriptors: https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz
      object-keys: https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz

  delayed-stream@https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz: {}

  doctrine@https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz:
    dependencies:
      esutils: https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz

  eastasianwidth@https://registry.npmmirror.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz: {}

  element-plus@https://registry.npmmirror.com/element-plus/-/element-plus-2.3.7.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz):
    dependencies:
      '@ctrl/tinycolor': https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.0.tgz
      '@element-plus/icons-vue': https://registry.npmmirror.com/@element-plus/icons-vue/-/icons-vue-2.1.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      '@floating-ui/dom': https://registry.npmmirror.com/@floating-ui/dom/-/dom-1.4.4.tgz
      '@popperjs/core': '@sxzz/popperjs-es@2.11.7'
      '@types/lodash': https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.195.tgz
      '@types/lodash-es': https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.7.tgz
      '@vueuse/core': https://registry.npmmirror.com/@vueuse/core/-/core-9.13.0.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      async-validator: https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz
      dayjs: https://registry.npmmirror.com/dayjs/-/dayjs-1.11.9.tgz
      escape-html: https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz
      lodash: https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz
      lodash-es: https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz
      lodash-unified: https://registry.npmmirror.com/lodash-unified/-/lodash-unified-1.0.3.tgz(@types/lodash-es@https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.7.tgz)(lodash-es@https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz)(lodash@https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz)
      memoize-one: https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz
      normalize-wheel-es: https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz
    transitivePeerDependencies:
      - '@vue/composition-api'

  emoji-regex@https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz: {}

  emoji-regex@https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz: {}

  esbuild@https://registry.npmmirror.com/esbuild/-/esbuild-0.17.19.tgz:
    optionalDependencies:
      '@esbuild/android-arm': 0.17.19
      '@esbuild/android-arm64': 0.17.19
      '@esbuild/android-x64': 0.17.19
      '@esbuild/darwin-arm64': 0.17.19
      '@esbuild/darwin-x64': 0.17.19
      '@esbuild/freebsd-arm64': 0.17.19
      '@esbuild/freebsd-x64': 0.17.19
      '@esbuild/linux-arm': 0.17.19
      '@esbuild/linux-arm64': 0.17.19
      '@esbuild/linux-ia32': 0.17.19
      '@esbuild/linux-loong64': 0.17.19
      '@esbuild/linux-mips64el': 0.17.19
      '@esbuild/linux-ppc64': 0.17.19
      '@esbuild/linux-riscv64': 0.17.19
      '@esbuild/linux-s390x': 0.17.19
      '@esbuild/linux-x64': 0.17.19
      '@esbuild/netbsd-x64': 0.17.19
      '@esbuild/openbsd-x64': 0.17.19
      '@esbuild/sunos-x64': 0.17.19
      '@esbuild/win32-arm64': 0.17.19
      '@esbuild/win32-ia32': 0.17.19
      '@esbuild/win32-x64': 0.17.19

  escape-html@https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz: {}

  escape-string-regexp@https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz: {}

  escape-string-regexp@https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz: {}

  eslint-config-prettier@https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.8.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz):
    dependencies:
      eslint: https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz

  eslint-plugin-prettier@https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz(eslint-config-prettier@https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.8.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz))(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)(prettier@https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz):
    dependencies:
      eslint: https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz
      prettier: https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz
      prettier-linter-helpers: https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz
    optionalDependencies:
      eslint-config-prettier: https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-8.8.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)

  eslint-plugin-vue@https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-9.11.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz):
    dependencies:
      '@eslint-community/eslint-utils': https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)
      eslint: https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz
      natural-compare: https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz
      nth-check: https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz
      postcss-selector-parser: https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz
      semver: https://registry.npmmirror.com/semver/-/semver-7.5.4.tgz
      vue-eslint-parser: https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.3.1.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)
      xml-name-validator: https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz
    transitivePeerDependencies:
      - supports-color

  eslint-scope@https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.0.tgz:
    dependencies:
      esrecurse: https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz
      estraverse: https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz

  eslint-visitor-keys@https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz: {}

  eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz:
    dependencies:
      '@eslint-community/eslint-utils': https://registry.npmmirror.com/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz)
      '@eslint-community/regexpp': https://registry.npmmirror.com/@eslint-community/regexpp/-/regexpp-4.5.1.tgz
      '@eslint/eslintrc': https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-2.1.0.tgz
      '@eslint/js': https://registry.npmmirror.com/@eslint/js/-/js-8.39.0.tgz
      '@humanwhocodes/config-array': https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.11.10.tgz
      '@humanwhocodes/module-importer': https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz
      '@nodelib/fs.walk': https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz
      ajv: https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz
      chalk: https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz
      cross-spawn: https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz
      debug: https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz
      doctrine: https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz
      escape-string-regexp: https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz
      eslint-scope: https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.0.tgz
      eslint-visitor-keys: https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz
      espree: https://registry.npmmirror.com/espree/-/espree-9.6.0.tgz
      esquery: https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz
      esutils: https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz
      fast-deep-equal: https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz
      file-entry-cache: https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz
      find-up: https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz
      glob-parent: https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz
      globals: https://registry.npmmirror.com/globals/-/globals-13.20.0.tgz
      grapheme-splitter: https://registry.npmmirror.com/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz
      ignore: https://registry.npmmirror.com/ignore/-/ignore-5.2.4.tgz
      import-fresh: https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz
      imurmurhash: https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz
      is-glob: https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz
      is-path-inside: https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz
      js-sdsl: https://registry.npmmirror.com/js-sdsl/-/js-sdsl-4.4.1.tgz
      js-yaml: https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz
      json-stable-stringify-without-jsonify: https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz
      levn: https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz
      lodash.merge: https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz
      minimatch: https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz
      natural-compare: https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz
      optionator: https://registry.npmmirror.com/optionator/-/optionator-0.9.3.tgz
      strip-ansi: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz
      strip-json-comments: https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz
      text-table: https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz
    transitivePeerDependencies:
      - supports-color

  espree@https://registry.npmmirror.com/espree/-/espree-9.6.0.tgz:
    dependencies:
      acorn: https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz
      acorn-jsx: https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz(acorn@https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz)
      eslint-visitor-keys: https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz

  esquery@https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz:
    dependencies:
      estraverse: https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz

  esrecurse@https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz:
    dependencies:
      estraverse: https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz

  estraverse@https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz: {}

  estree-walker@https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz: {}

  esutils@https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz: {}

  eventemitter3@https://registry.npmmirror.com/eventemitter3/-/eventemitter3-2.0.3.tgz: {}

  execa@https://registry.npmmirror.com/execa/-/execa-7.1.1.tgz:
    dependencies:
      cross-spawn: https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz
      get-stream: https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz
      human-signals: https://registry.npmmirror.com/human-signals/-/human-signals-4.3.1.tgz
      is-stream: https://registry.npmmirror.com/is-stream/-/is-stream-3.0.0.tgz
      merge-stream: https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz
      npm-run-path: https://registry.npmmirror.com/npm-run-path/-/npm-run-path-5.1.0.tgz
      onetime: https://registry.npmmirror.com/onetime/-/onetime-6.0.0.tgz
      signal-exit: https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz
      strip-final-newline: https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-3.0.0.tgz

  extend@https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz: {}

  fast-deep-equal@https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz: {}

  fast-diff@https://registry.npmmirror.com/fast-diff/-/fast-diff-1.1.2.tgz: {}

  fast-diff@https://registry.npmmirror.com/fast-diff/-/fast-diff-1.2.0.tgz: {}

  fast-diff@https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz: {}

  fast-glob@https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.0.tgz:
    dependencies:
      '@nodelib/fs.stat': https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz
      '@nodelib/fs.walk': https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz
      glob-parent: https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz
      merge2: https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz
      micromatch: https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz

  fast-json-stable-stringify@https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz: {}

  fast-levenshtein@https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz: {}

  fastq@https://registry.npmmirror.com/fastq/-/fastq-1.15.0.tgz:
    dependencies:
      reusify: https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz

  file-entry-cache@https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz:
    dependencies:
      flat-cache: https://registry.npmmirror.com/flat-cache/-/flat-cache-3.0.4.tgz

  fill-range@https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz:
    dependencies:
      to-regex-range: https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz

  find-up@https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz:
    dependencies:
      locate-path: https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz
      path-exists: https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz

  flat-cache@https://registry.npmmirror.com/flat-cache/-/flat-cache-3.0.4.tgz:
    dependencies:
      flatted: https://registry.npmmirror.com/flatted/-/flatted-3.2.7.tgz
      rimraf: https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz

  flatted@https://registry.npmmirror.com/flatted/-/flatted-3.2.7.tgz: {}

  follow-redirects@https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.2.tgz: {}

  form-data@https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz:
    dependencies:
      asynckit: https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz
      combined-stream: https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz
      mime-types: https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz

  fs.realpath@https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz: {}

  fsevents@2.3.2:
    optional: true

  function-bind@https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz: {}

  functions-have-names@https://registry.npmmirror.com/functions-have-names/-/functions-have-names-1.2.3.tgz: {}

  get-intrinsic@https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.1.tgz:
    dependencies:
      function-bind: https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz
      has: https://registry.npmmirror.com/has/-/has-1.0.3.tgz
      has-proto: https://registry.npmmirror.com/has-proto/-/has-proto-1.0.1.tgz
      has-symbols: https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz

  get-stream@https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz: {}

  glob-parent@https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz:
    dependencies:
      is-glob: https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz

  glob-parent@https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz:
    dependencies:
      is-glob: https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz

  glob@https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz:
    dependencies:
      fs.realpath: https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz
      inflight: https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz
      inherits: https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz
      minimatch: https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz
      once: https://registry.npmmirror.com/once/-/once-1.4.0.tgz
      path-is-absolute: https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz

  globals@https://registry.npmmirror.com/globals/-/globals-13.20.0.tgz:
    dependencies:
      type-fest: https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz

  grapheme-splitter@https://registry.npmmirror.com/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz: {}

  has-flag@https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz: {}

  has-property-descriptors@https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz:
    dependencies:
      get-intrinsic: https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.1.tgz

  has-proto@https://registry.npmmirror.com/has-proto/-/has-proto-1.0.1.tgz: {}

  has-symbols@https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz: {}

  has-tostringtag@https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz:
    dependencies:
      has-symbols: https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz

  has@https://registry.npmmirror.com/has/-/has-1.0.3.tgz:
    dependencies:
      function-bind: https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz

  human-signals@https://registry.npmmirror.com/human-signals/-/human-signals-4.3.1.tgz: {}

  husky@https://registry.npmmirror.com/husky/-/husky-8.0.0.tgz: {}

  ignore@https://registry.npmmirror.com/ignore/-/ignore-5.2.4.tgz: {}

  immutable@https://registry.npmmirror.com/immutable/-/immutable-4.3.0.tgz: {}

  import-fresh@https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz:
    dependencies:
      parent-module: https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz
      resolve-from: https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz

  imurmurhash@https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz: {}

  indent-string@https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz: {}

  inflight@https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz:
    dependencies:
      once: https://registry.npmmirror.com/once/-/once-1.4.0.tgz
      wrappy: https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz

  inherits@https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz: {}

  is-arguments@https://registry.npmmirror.com/is-arguments/-/is-arguments-1.1.1.tgz:
    dependencies:
      call-bind: https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz
      has-tostringtag: https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz

  is-binary-path@https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz:
    dependencies:
      binary-extensions: https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz

  is-core-module@https://registry.npmmirror.com/is-core-module/-/is-core-module-2.12.1.tgz:
    dependencies:
      has: https://registry.npmmirror.com/has/-/has-1.0.3.tgz

  is-date-object@https://registry.npmmirror.com/is-date-object/-/is-date-object-1.0.5.tgz:
    dependencies:
      has-tostringtag: https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz

  is-extglob@https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz: {}

  is-fullwidth-code-point@https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz: {}

  is-fullwidth-code-point@https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz: {}

  is-glob@https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz:
    dependencies:
      is-extglob: https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz

  is-number@https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz: {}

  is-path-inside@https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz: {}

  is-regex@https://registry.npmmirror.com/is-regex/-/is-regex-1.1.4.tgz:
    dependencies:
      call-bind: https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz
      has-tostringtag: https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz

  is-stream@https://registry.npmmirror.com/is-stream/-/is-stream-3.0.0.tgz: {}

  isexe@https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz: {}

  js-sdsl@https://registry.npmmirror.com/js-sdsl/-/js-sdsl-4.4.1.tgz: {}

  js-yaml@https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz:
    dependencies:
      argparse: https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz

  json-schema-traverse@https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz: {}

  json-stable-stringify-without-jsonify@https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz: {}

  jsonc-parser@https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.2.0.tgz: {}

  levn@https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz:
    dependencies:
      prelude-ls: https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz
      type-check: https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz

  lilconfig@https://registry.npmmirror.com/lilconfig/-/lilconfig-2.1.0.tgz: {}

  lint-staged@https://registry.npmmirror.com/lint-staged/-/lint-staged-13.2.3.tgz:
    dependencies:
      chalk: https://registry.npmmirror.com/chalk/-/chalk-5.2.0.tgz
      cli-truncate: https://registry.npmmirror.com/cli-truncate/-/cli-truncate-3.1.0.tgz
      commander: https://registry.npmmirror.com/commander/-/commander-10.0.1.tgz
      debug: https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz
      execa: https://registry.npmmirror.com/execa/-/execa-7.1.1.tgz
      lilconfig: https://registry.npmmirror.com/lilconfig/-/lilconfig-2.1.0.tgz
      listr2: https://registry.npmmirror.com/listr2/-/listr2-5.0.8.tgz
      micromatch: https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz
      normalize-path: https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz
      object-inspect: https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.3.tgz
      pidtree: https://registry.npmmirror.com/pidtree/-/pidtree-0.6.0.tgz
      string-argv: https://registry.npmmirror.com/string-argv/-/string-argv-0.3.2.tgz
      yaml: https://registry.npmmirror.com/yaml/-/yaml-2.3.1.tgz
    transitivePeerDependencies:
      - enquirer
      - supports-color

  listr2@https://registry.npmmirror.com/listr2/-/listr2-5.0.8.tgz:
    dependencies:
      cli-truncate: https://registry.npmmirror.com/cli-truncate/-/cli-truncate-2.1.0.tgz
      colorette: https://registry.npmmirror.com/colorette/-/colorette-2.0.20.tgz
      log-update: https://registry.npmmirror.com/log-update/-/log-update-4.0.0.tgz
      p-map: https://registry.npmmirror.com/p-map/-/p-map-4.0.0.tgz
      rfdc: https://registry.npmmirror.com/rfdc/-/rfdc-1.3.0.tgz
      rxjs: https://registry.npmmirror.com/rxjs/-/rxjs-7.8.1.tgz
      through: https://registry.npmmirror.com/through/-/through-2.3.8.tgz
      wrap-ansi: https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz

  local-pkg@https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.3.tgz: {}

  locate-path@https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz:
    dependencies:
      p-locate: https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz

  lodash-es@https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz: {}

  lodash-unified@https://registry.npmmirror.com/lodash-unified/-/lodash-unified-1.0.3.tgz(@types/lodash-es@https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.7.tgz)(lodash-es@https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz)(lodash@https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz):
    dependencies:
      '@types/lodash-es': https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.7.tgz
      lodash: https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz
      lodash-es: https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz

  lodash.clonedeep@https://registry.npmmirror.com/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz: {}

  lodash.isequal@https://registry.npmmirror.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz: {}

  lodash.merge@https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz: {}

  lodash@https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz: {}

  log-update@https://registry.npmmirror.com/log-update/-/log-update-4.0.0.tgz:
    dependencies:
      ansi-escapes: https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz
      cli-cursor: https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz
      slice-ansi: https://registry.npmmirror.com/slice-ansi/-/slice-ansi-4.0.0.tgz
      wrap-ansi: https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-6.2.0.tgz

  lru-cache@https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz:
    dependencies:
      yallist: https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz

  magic-string@https://registry.npmmirror.com/magic-string/-/magic-string-0.30.1.tgz:
    dependencies:
      '@jridgewell/sourcemap-codec': https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz

  memoize-one@https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz: {}

  merge-stream@https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz: {}

  merge2@https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz: {}

  micromatch@https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz:
    dependencies:
      braces: https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz
      picomatch: https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz

  mime-db@https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz: {}

  mime-types@https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz:
    dependencies:
      mime-db: https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz

  mimic-fn@https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz: {}

  mimic-fn@https://registry.npmmirror.com/mimic-fn/-/mimic-fn-4.0.0.tgz: {}

  minimatch@https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz:
    dependencies:
      brace-expansion: https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz

  minimatch@https://registry.npmmirror.com/minimatch/-/minimatch-9.0.3.tgz:
    dependencies:
      brace-expansion: https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz

  mlly@https://registry.npmmirror.com/mlly/-/mlly-1.4.0.tgz:
    dependencies:
      acorn: https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz
      pathe: https://registry.npmmirror.com/pathe/-/pathe-1.1.1.tgz
      pkg-types: https://registry.npmmirror.com/pkg-types/-/pkg-types-1.0.3.tgz
      ufo: https://registry.npmmirror.com/ufo/-/ufo-1.1.2.tgz

  ms@https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz: {}

  nanoid@https://registry.npmmirror.com/nanoid/-/nanoid-3.3.6.tgz: {}

  natural-compare@https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz: {}

  normalize-path@https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz: {}

  normalize-wheel-es@https://registry.npmmirror.com/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz: {}

  npm-run-path@https://registry.npmmirror.com/npm-run-path/-/npm-run-path-5.1.0.tgz:
    dependencies:
      path-key: https://registry.npmmirror.com/path-key/-/path-key-4.0.0.tgz

  nth-check@https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz:
    dependencies:
      boolbase: https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz

  object-inspect@https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.3.tgz: {}

  object-is@https://registry.npmmirror.com/object-is/-/object-is-1.1.5.tgz:
    dependencies:
      call-bind: https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz
      define-properties: https://registry.npmmirror.com/define-properties/-/define-properties-1.2.0.tgz

  object-keys@https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz: {}

  once@https://registry.npmmirror.com/once/-/once-1.4.0.tgz:
    dependencies:
      wrappy: https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz

  onetime@https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz:
    dependencies:
      mimic-fn: https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz

  onetime@https://registry.npmmirror.com/onetime/-/onetime-6.0.0.tgz:
    dependencies:
      mimic-fn: https://registry.npmmirror.com/mimic-fn/-/mimic-fn-4.0.0.tgz

  optionator@https://registry.npmmirror.com/optionator/-/optionator-0.9.3.tgz:
    dependencies:
      '@aashutoshrathi/word-wrap': https://registry.npmmirror.com/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz
      deep-is: https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz
      fast-levenshtein: https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz
      levn: https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz
      prelude-ls: https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz
      type-check: https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz

  p-limit@https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz:
    dependencies:
      yocto-queue: https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz

  p-locate@https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz:
    dependencies:
      p-limit: https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz

  p-map@https://registry.npmmirror.com/p-map/-/p-map-4.0.0.tgz:
    dependencies:
      aggregate-error: https://registry.npmmirror.com/aggregate-error/-/aggregate-error-3.1.0.tgz

  parchment@https://registry.npmmirror.com/parchment/-/parchment-1.1.4.tgz: {}

  parent-module@https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz:
    dependencies:
      callsites: https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz

  path-exists@https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz: {}

  path-is-absolute@https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz: {}

  path-key@https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz: {}

  path-key@https://registry.npmmirror.com/path-key/-/path-key-4.0.0.tgz: {}

  path-parse@https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz: {}

  pathe@https://registry.npmmirror.com/pathe/-/pathe-1.1.1.tgz: {}

  picocolors@https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz: {}

  picomatch@https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz: {}

  pidtree@https://registry.npmmirror.com/pidtree/-/pidtree-0.6.0.tgz: {}

  pinia-plugin-persistedstate@https://registry.npmmirror.com/pinia-plugin-persistedstate/-/pinia-plugin-persistedstate-3.1.0.tgz(pinia@https://registry.npmmirror.com/pinia/-/pinia-2.1.3.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)):
    dependencies:
      pinia: https://registry.npmmirror.com/pinia/-/pinia-2.1.3.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)

  pinia@https://registry.npmmirror.com/pinia/-/pinia-2.1.3.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz):
    dependencies:
      '@vue/devtools-api': https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.5.0.tgz
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz
      vue-demi: https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.5.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)

  pkg-types@https://registry.npmmirror.com/pkg-types/-/pkg-types-1.0.3.tgz:
    dependencies:
      jsonc-parser: https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.2.0.tgz
      mlly: https://registry.npmmirror.com/mlly/-/mlly-1.4.0.tgz
      pathe: https://registry.npmmirror.com/pathe/-/pathe-1.1.1.tgz

  postcss-selector-parser@https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz:
    dependencies:
      cssesc: https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz
      util-deprecate: https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz

  postcss@https://registry.npmmirror.com/postcss/-/postcss-8.4.25.tgz:
    dependencies:
      nanoid: https://registry.npmmirror.com/nanoid/-/nanoid-3.3.6.tgz
      picocolors: https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz
      source-map-js: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz

  prelude-ls@https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz: {}

  prettier-linter-helpers@https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz:
    dependencies:
      fast-diff: https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz

  prettier@https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz: {}

  proxy-from-env@https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz: {}

  punycode@https://registry.npmmirror.com/punycode/-/punycode-2.3.0.tgz: {}

  queue-microtask@https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz: {}

  quill-delta@https://registry.npmmirror.com/quill-delta/-/quill-delta-3.6.3.tgz:
    dependencies:
      deep-equal: https://registry.npmmirror.com/deep-equal/-/deep-equal-1.1.1.tgz
      extend: https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz
      fast-diff: https://registry.npmmirror.com/fast-diff/-/fast-diff-1.1.2.tgz

  quill-delta@https://registry.npmmirror.com/quill-delta/-/quill-delta-4.2.2.tgz:
    dependencies:
      fast-diff: https://registry.npmmirror.com/fast-diff/-/fast-diff-1.2.0.tgz
      lodash.clonedeep: https://registry.npmmirror.com/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz
      lodash.isequal: https://registry.npmmirror.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz

  quill@https://registry.npmmirror.com/quill/-/quill-1.3.7.tgz:
    dependencies:
      clone: https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz
      deep-equal: https://registry.npmmirror.com/deep-equal/-/deep-equal-1.1.1.tgz
      eventemitter3: https://registry.npmmirror.com/eventemitter3/-/eventemitter3-2.0.3.tgz
      extend: https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz
      parchment: https://registry.npmmirror.com/parchment/-/parchment-1.1.4.tgz
      quill-delta: https://registry.npmmirror.com/quill-delta/-/quill-delta-3.6.3.tgz

  readdirp@https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz:
    dependencies:
      picomatch: https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz

  regexp.prototype.flags@https://registry.npmmirror.com/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz:
    dependencies:
      call-bind: https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz
      define-properties: https://registry.npmmirror.com/define-properties/-/define-properties-1.2.0.tgz
      functions-have-names: https://registry.npmmirror.com/functions-have-names/-/functions-have-names-1.2.3.tgz

  resolve-from@https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz: {}

  resolve@https://registry.npmmirror.com/resolve/-/resolve-1.22.2.tgz:
    dependencies:
      is-core-module: https://registry.npmmirror.com/is-core-module/-/is-core-module-2.12.1.tgz
      path-parse: https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz
      supports-preserve-symlinks-flag: https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz

  restore-cursor@https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz:
    dependencies:
      onetime: https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz
      signal-exit: https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz

  reusify@https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz: {}

  rfdc@https://registry.npmmirror.com/rfdc/-/rfdc-1.3.0.tgz: {}

  rimraf@https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz:
    dependencies:
      glob: https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz

  rollup@3.26.2:
    optionalDependencies:
      fsevents: 2.3.2
    optional: true

  rollup@https://registry.npmmirror.com/rollup/-/rollup-3.26.2.tgz:
    optionalDependencies:
      fsevents: 2.3.2

  run-parallel@https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz:
    dependencies:
      queue-microtask: https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz

  rxjs@https://registry.npmmirror.com/rxjs/-/rxjs-7.8.1.tgz:
    dependencies:
      tslib: https://registry.npmmirror.com/tslib/-/tslib-2.6.0.tgz

  sass@https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz:
    dependencies:
      chokidar: https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz
      immutable: https://registry.npmmirror.com/immutable/-/immutable-4.3.0.tgz
      source-map-js: https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz

  scule@https://registry.npmmirror.com/scule/-/scule-1.0.0.tgz: {}

  semver@https://registry.npmmirror.com/semver/-/semver-7.5.4.tgz:
    dependencies:
      lru-cache: https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz

  shebang-command@https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz:
    dependencies:
      shebang-regex: https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz

  shebang-regex@https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz: {}

  signal-exit@https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz: {}

  slice-ansi@https://registry.npmmirror.com/slice-ansi/-/slice-ansi-3.0.0.tgz:
    dependencies:
      ansi-styles: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz
      astral-regex: https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz
      is-fullwidth-code-point: https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz

  slice-ansi@https://registry.npmmirror.com/slice-ansi/-/slice-ansi-4.0.0.tgz:
    dependencies:
      ansi-styles: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz
      astral-regex: https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz
      is-fullwidth-code-point: https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz

  slice-ansi@https://registry.npmmirror.com/slice-ansi/-/slice-ansi-5.0.0.tgz:
    dependencies:
      ansi-styles: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-6.2.1.tgz
      is-fullwidth-code-point: https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz

  source-map-js@https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz: {}

  spark-md5@3.0.2: {}

  string-argv@https://registry.npmmirror.com/string-argv/-/string-argv-0.3.2.tgz: {}

  string-width@https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz:
    dependencies:
      emoji-regex: https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz
      is-fullwidth-code-point: https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz
      strip-ansi: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz

  string-width@https://registry.npmmirror.com/string-width/-/string-width-5.1.2.tgz:
    dependencies:
      eastasianwidth: https://registry.npmmirror.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz
      emoji-regex: https://registry.npmmirror.com/emoji-regex/-/emoji-regex-9.2.2.tgz
      strip-ansi: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.1.0.tgz

  strip-ansi@https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz:
    dependencies:
      ansi-regex: https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz

  strip-ansi@https://registry.npmmirror.com/strip-ansi/-/strip-ansi-7.1.0.tgz:
    dependencies:
      ansi-regex: https://registry.npmmirror.com/ansi-regex/-/ansi-regex-6.0.1.tgz

  strip-final-newline@https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-3.0.0.tgz: {}

  strip-json-comments@https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz: {}

  strip-literal@https://registry.npmmirror.com/strip-literal/-/strip-literal-1.0.1.tgz:
    dependencies:
      acorn: https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz

  supports-color@https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz:
    dependencies:
      has-flag: https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz

  supports-preserve-symlinks-flag@https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz: {}

  text-table@https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz: {}

  through@https://registry.npmmirror.com/through/-/through-2.3.8.tgz: {}

  to-fast-properties@2.0.0:
    optional: true

  to-fast-properties@https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz: {}

  to-regex-range@https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz:
    dependencies:
      is-number: https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz

  tslib@https://registry.npmmirror.com/tslib/-/tslib-2.6.0.tgz: {}

  type-check@https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz:
    dependencies:
      prelude-ls: https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz

  type-fest@https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz: {}

  type-fest@https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz: {}

  ufo@https://registry.npmmirror.com/ufo/-/ufo-1.1.2.tgz: {}

  unimport@https://registry.npmmirror.com/unimport/-/unimport-3.0.14.tgz(rollup@3.26.2):
    dependencies:
      '@rollup/pluginutils': https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-5.0.2.tgz(rollup@3.26.2)
      escape-string-regexp: https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz
      fast-glob: https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.0.tgz
      local-pkg: https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.3.tgz
      magic-string: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.1.tgz
      mlly: https://registry.npmmirror.com/mlly/-/mlly-1.4.0.tgz
      pathe: https://registry.npmmirror.com/pathe/-/pathe-1.1.1.tgz
      pkg-types: https://registry.npmmirror.com/pkg-types/-/pkg-types-1.0.3.tgz
      scule: https://registry.npmmirror.com/scule/-/scule-1.0.0.tgz
      strip-literal: https://registry.npmmirror.com/strip-literal/-/strip-literal-1.0.1.tgz
      unplugin: https://registry.npmmirror.com/unplugin/-/unplugin-1.3.2.tgz
    transitivePeerDependencies:
      - rollup

  unplugin-auto-import@https://registry.npmmirror.com/unplugin-auto-import/-/unplugin-auto-import-0.16.6.tgz(@vueuse/core@9.13.0(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz))(rollup@3.26.2):
    dependencies:
      '@antfu/utils': https://registry.npmmirror.com/@antfu/utils/-/utils-0.7.5.tgz
      '@rollup/pluginutils': https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-5.0.2.tgz(rollup@3.26.2)
      fast-glob: https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.0.tgz
      local-pkg: https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.3.tgz
      magic-string: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.1.tgz
      minimatch: https://registry.npmmirror.com/minimatch/-/minimatch-9.0.3.tgz
      unimport: https://registry.npmmirror.com/unimport/-/unimport-3.0.14.tgz(rollup@3.26.2)
      unplugin: https://registry.npmmirror.com/unplugin/-/unplugin-1.3.2.tgz
    optionalDependencies:
      '@vueuse/core': 9.13.0(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
    transitivePeerDependencies:
      - rollup

  unplugin-vue-components@https://registry.npmmirror.com/unplugin-vue-components/-/unplugin-vue-components-0.25.1.tgz(@babel/parser@7.22.7)(rollup@3.26.2)(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz):
    dependencies:
      '@antfu/utils': https://registry.npmmirror.com/@antfu/utils/-/utils-0.7.5.tgz
      '@rollup/pluginutils': https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-5.0.2.tgz(rollup@3.26.2)
      chokidar: https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz
      debug: https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz
      fast-glob: https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.0.tgz
      local-pkg: https://registry.npmmirror.com/local-pkg/-/local-pkg-0.4.3.tgz
      magic-string: https://registry.npmmirror.com/magic-string/-/magic-string-0.30.1.tgz
      minimatch: https://registry.npmmirror.com/minimatch/-/minimatch-9.0.3.tgz
      resolve: https://registry.npmmirror.com/resolve/-/resolve-1.22.2.tgz
      unplugin: https://registry.npmmirror.com/unplugin/-/unplugin-1.3.2.tgz
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz
    optionalDependencies:
      '@babel/parser': 7.22.7
    transitivePeerDependencies:
      - rollup
      - supports-color

  unplugin@https://registry.npmmirror.com/unplugin/-/unplugin-1.3.2.tgz:
    dependencies:
      acorn: https://registry.npmmirror.com/acorn/-/acorn-8.10.0.tgz
      chokidar: https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz
      webpack-sources: https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz
      webpack-virtual-modules: https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.5.0.tgz

  uri-js@https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz:
    dependencies:
      punycode: https://registry.npmmirror.com/punycode/-/punycode-2.3.0.tgz

  util-deprecate@https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz: {}

  vite@https://registry.npmmirror.com/vite/-/vite-4.3.9.tgz(sass@https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz):
    dependencies:
      esbuild: https://registry.npmmirror.com/esbuild/-/esbuild-0.17.19.tgz
      postcss: https://registry.npmmirror.com/postcss/-/postcss-8.4.25.tgz
      rollup: https://registry.npmmirror.com/rollup/-/rollup-3.26.2.tgz
    optionalDependencies:
      fsevents: 2.3.2
      sass: https://registry.npmmirror.com/sass/-/sass-1.63.6.tgz

  vue-demi@0.14.5(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz):
    dependencies:
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz
    optional: true

  vue-demi@https://registry.npmmirror.com/vue-demi/-/vue-demi-0.14.5.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz):
    dependencies:
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz

  vue-eslint-parser@https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-9.3.1.tgz(eslint@https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz):
    dependencies:
      debug: https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz
      eslint: https://registry.npmmirror.com/eslint/-/eslint-8.39.0.tgz
      eslint-scope: https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.0.tgz
      eslint-visitor-keys: https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz
      espree: https://registry.npmmirror.com/espree/-/espree-9.6.0.tgz
      esquery: https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz
      lodash: https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz
      semver: https://registry.npmmirror.com/semver/-/semver-7.5.4.tgz
    transitivePeerDependencies:
      - supports-color

  vue-router@https://registry.npmmirror.com/vue-router/-/vue-router-4.2.2.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz):
    dependencies:
      '@vue/devtools-api': https://registry.npmmirror.com/@vue/devtools-api/-/devtools-api-6.5.0.tgz
      vue: https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz

  vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz:
    dependencies:
      '@vue/compiler-dom': https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.3.4.tgz
      '@vue/compiler-sfc': https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.3.4.tgz
      '@vue/runtime-dom': https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.3.4.tgz
      '@vue/server-renderer': https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.3.4.tgz(vue@https://registry.npmmirror.com/vue/-/vue-3.3.4.tgz)
      '@vue/shared': https://registry.npmmirror.com/@vue/shared/-/shared-3.3.4.tgz

  webpack-sources@https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz: {}

  webpack-virtual-modules@https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.5.0.tgz: {}

  which@https://registry.npmmirror.com/which/-/which-2.0.2.tgz:
    dependencies:
      isexe: https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz

  wrap-ansi@https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-6.2.0.tgz:
    dependencies:
      ansi-styles: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz
      string-width: https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz
      strip-ansi: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz

  wrap-ansi@https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz:
    dependencies:
      ansi-styles: https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz
      string-width: https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz
      strip-ansi: https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz

  wrappy@https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz: {}

  xml-name-validator@https://registry.npmmirror.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz: {}

  yallist@https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz: {}

  yaml@https://registry.npmmirror.com/yaml/-/yaml-2.3.1.tgz: {}

  yocto-queue@https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz: {}
