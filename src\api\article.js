import http from '@/utils/http'

export const artGetChannelsService = (params) =>
  http.get('/objectStorage/beforeList', {
    params
  })

export const artAddChannelService = (data) =>
  http.post('/objectStorage/beforeSave', data)

export const artEditChannelService = (data) =>
  http.put('/objectStorage/beforeUpdate', data)

export const artDelChannelService = (id) =>
  http.delete('/objectStorage/beforeDelete', {
    params: { id }
  })

export const artGetListService = (params) =>
  http.get('/dataSource/list', {
    params
  })

export const artPublishService = (data) => http.post('/dataSource/save', data)

export const artEditService = (data) => http.put('/dataSource/update', data)

export const artDelService = (id, filePath) =>
  http.delete('/dataSource/delete', { params: { id, filePath } })

export const uploadFile = (data, obj) =>
  http.post('/minio/uploadFile', data, obj)

export const updateStatus = (data) =>
  http.post('/dataSource/updateStatus', data)

export const downloadFile = (filePath) =>
  http.get('/minio/getURL', { params: { filePath } })
