import { defineStore } from 'pinia'
import { ref } from 'vue'

// 数字计数器模块
export const useManageStore = defineStore(
  'manage',
  () => {
    const manage = ref({
      pageNum: 1, // 当前页
      pageSize: 10, // 当前生效的每页条数
      spaceCode: '',
      dataStatus: ''
    })
    const setManage = (data) => {
      manage.value = data
    }
    return {
      manage,
      setManage
    }
  },
  {
    persist: true
  }
)
