<script setup>
import { ref } from 'vue'
import { Edit, Delete } from '@element-plus/icons-vue'
import { artGetChannelsService, artDelChannelService } from '../../api/article'
import ChannelEdit from './components/ChannelEdit.vue'
const channelList = ref([])
const loading = ref(false)
const dialog = ref()
const total = ref(0)
const params = ref({
  pageNum: 1, // 当前页
  pageSize: 10, // 当前生效的每页条数
  userId: ''
})

const getChannelList = async () => {
  loading.value = true
  const res = await artGetChannelsService(params.value)
  channelList.value = res.data.data
  total.value = res.data.total
  loading.value = false
}
getChannelList()

const onDelChannel = async (row) => {
  await ElMessageBox.confirm('你确认要删除该对象存储空间么', '温馨提示', {
    type: 'warning',
    confirmButtonText: '确认',
    cancelButtonText: '取消'
  })
  await artDelChannelService(row.id)
  ElMessage.success('删除成功')
  getChannelList()
}
const onSizeChange = (size) => {
  params.value.pageNum = 1
  params.value.pageSize = size
  getChannelList()
}
const onCurrentChange = (page) => {
  params.value.pageNum = page
  getChannelList()
}
const onEditChannel = (row) => {
  dialog.value.open(row)
}
const onAddChannel = () => {
  dialog.value.open({})
}
const onSuccess = () => {
  getChannelList()
}
</script>

<template>
  <page-container title="对象存储空间管理">
    <template #extra>
      <el-button type="primary" @click="onAddChannel"
        >新建对象存储空间
      </el-button>
    </template>

    <el-table v-loading="loading" :data="channelList" style="width: 100%">
      <el-table-column type="index" label="序号" width="100"></el-table-column>
      <el-table-column prop="spaceName" label="对象存储空间名称" align="center">
        <template #default="{ row }">
          <router-link
            :to="{
              path: '/article/manage',
              query: {
                spaceCode: row.spaceCode
              }
            }"
          >
            {{ row.spaceName }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="spaceCode"
        label="对象存储空间编号"
        align="center"
      ></el-table-column>
      <el-table-column prop="spaceCapacity" label="容量" align="center">
        <template #default="{ row }">
          {{
            row.spaceCapacity > 1048576
              ? parseInt(row.spaceCapacity / 1048576) + 'MB'
              : row.spaceCapacity > 1024
              ? parseInt(row.spaceCapacity / 1024) + 'KB'
              : row.spaceCapacity
              ? row.spaceCapacity + 'B'
              : 0
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="fileNum"
        label="文件数"
        align="center"
      ></el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template #default="{ row }">
          {{ row.createTime.split('T').join(' ') }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" align="center">
        <template #default="{ row }">
          {{ row.updateTime.split('T').join(' ') }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center">
        <template #default="{ row, $index }">
          <el-button
            :icon="Edit"
            circle
            plain
            type="primary"
            @click="onEditChannel(row, $index)"
          ></el-button>
          <el-button
            :icon="Delete"
            circle
            plain
            type="danger"
            @click="onDelChannel(row, $index)"
          ></el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>

    <el-pagination
      v-model:current-page="params.pageNum"
      v-model:page-size="params.pageSize"
      :page-sizes="[2, 3, 5, 10]"
      :background="true"
      layout="jumper, total, sizes, prev, pager, next"
      :total="total"
      @size-change="onSizeChange"
      @current-change="onCurrentChange"
      style="margin-top: 20px; justify-content: flex-end"
    />

    <channel-edit ref="dialog" @success="onSuccess"></channel-edit>
  </page-container>
</template>

<style lang="scss" scoped>
a {
  text-decoration: none;
  color: #409eff;
}
</style>
