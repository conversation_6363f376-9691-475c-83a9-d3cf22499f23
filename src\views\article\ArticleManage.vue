<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Delete, Edit, Download } from '@element-plus/icons-vue'
import ChannelSelect from './components/ChannelSelect.vue'
import ArticleEdit from './components/ArticleEdit.vue'
import { useManageStore } from '@/stores'
import {
  artGetListService,
  artDelService,
  downloadFile
} from '@/api/article.js'
const route = useRoute()
const router = useRouter()
const articleList = ref([])
const total = ref(0) // 总条数
const loading = ref(false) // loading状态
const manageStore = useManageStore()
// 定义请求参数对象
const params = ref({
  pageNum: 1, // 当前页
  pageSize: 10, // 当前生效的每页条数
  spaceCode: '',
  dataStatus: ''
})

onMounted(() => {
  params.value = manageStore.manage
  params.value.spaceCode = route.query.spaceCode || params.value.spaceCode
  getArticleList()
})

onUnmounted(() => {
  params.value = {
    pageNum: 1,
    pageSize: 10,
    spaceCode: '',
    dataStatus: ''
  }
  manageStore.setManage(params.value)
})
// 基于params参数，获取文章列表
const getArticleList = async () => {
  loading.value = true
  const res = await artGetListService(params.value)
  articleList.value = res.data.data
  total.value = res.data.total
  loading.value = false
  // manageStore.setManage(params.value)
}

// 处理分页逻辑
const onSizeChange = (size) => {
  // 重新从第一页渲染即可
  params.value.pageNum = 1
  params.value.pageSize = size
  manageStore.setManage(params.value)
  // 基于最新的当前页 和 每页条数，渲染数据
  getArticleList()
}
const onCurrentChange = (page) => {
  // 更新当前页
  params.value.pageNum = page
  manageStore.setManage(params.value)
  // 基于最新的当前页，渲染数据
  getArticleList()
}

// 搜索逻辑
const onSearch = () => {
  params.value.pageNum = 1 // 重置页面
  manageStore.setManage(params.value)
  router.replace({
    path: '/article/manage',
    query: {
      spaceCode: params.value.spaceCode
    }
  })
  getArticleList(params)
}

// 重置逻辑
const onReset = () => {
  params.value.pageNum = 1 // 重置页面
  params.value.spaceCode = ''
  params.value.dataStatus = ''
  router.replace('/article/manage')
  manageStore.setManage(params.value)
  getArticleList()
}

const articleEditRef = ref()
// 添加逻辑
const onAddArticle = () => {
  articleEditRef.value.open({ spaceCode: params.value.spaceCode })
}
// 编辑逻辑
const onEditArticle = (row) => {
  articleEditRef.value.open(row)
}

const onDownloadArticle = async (row) => {
  const res = await downloadFile(row.filePath)
  const url = res.data.data
  const link = document.createElement('a')
  link.href = url
  link.setAttribute('download', '')
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 删除逻辑
const onDeleteArticle = async (row) => {
  // 提示用户是否要删除
  await ElMessageBox.confirm('此操作将永久删除该文件, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  await artDelService(row.id, row.filePath)
  ElMessage.success('删除成功')
  manageStore.setManage(params.value)
  // 重新渲染列表
  getArticleList()
}

// 添加或者编辑 成功的回调
const onSuccess = (type) => {
  if (type.name === 'add') {
    // 如果是添加，最好渲染最后一页
    // const lastPage = Math.ceil((total.value + 1) / params.value.pageSize)
    // 更新成最大页码数，再渲染
    params.value.pageNum = 1
  }
  params.value.spaceCode = type.spaceCode
  router.replace({
    path: '/article/manage',
    query: {
      spaceCode: params.value.spaceCode
    }
  })
  manageStore.setManage(params.value)
  getArticleList()
}
</script>

<template>
  <page-container title="数据源管理">
    <template #extra>
      <el-button type="primary" @click="onAddArticle">添加文件</el-button>
    </template>

    <!-- 表单区域 -->
    <el-form inline>
      <el-form-item label="对象存储空间:">
        <channel-select v-model="params.spaceCode"></channel-select>
      </el-form-item>
      <el-form-item label="上传状态:">
        <el-select v-model="params.dataStatus">
          <el-option label="成功" value="SUCCESS"></el-option>
          <el-option label="失败" value="FAIL"></el-option>
          <el-option label="上传中" value="UPLOADING"></el-option>
          <el-option label="校验中" value="CHECKING"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          @click="onSearch"
          type="primary"
          :disabled="!params.spaceCode"
          >搜索
        </el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <el-table :data="articleList" v-loading="loading">
      <el-table-column
        type="index"
        label="序号"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="dataName"
        label="数据名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="dataType"
        label="数据类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="dataFormat"
        label="文件格式"
        align="center"
      ></el-table-column>
      <el-table-column prop="dataCapacity" label="容量" align="center">
        <template #default="{ row }">
          {{
            row.dataCapacity > 1048576
              ? (row.dataCapacity / 1048576).toFixed(2) + 'MB'
              : row.dataCapacity > 1024
              ? Math.round(row.dataCapacity / 1024) + 'KB'
              : row.dataCapacity + 'B'
          }}
        </template>
      </el-table-column>
      <el-table-column prop="dataStatus" label="状态" align="center">
        <template #default="{ row }">
          <el-text
            :type="
              row.dataStatus === 'SUCCESS'
                ? 'success'
                : row.dataStatus === 'UPLOADING'
                ? 'primary'
                : row.dataStatus === 'CHECKING'
                ? 'warning'
                : 'danger'
            "
          >
            {{
              row.dataStatus === 'SUCCESS'
                ? '成功'
                : row.dataStatus === 'UPLOADING'
                ? '上传中'
                : row.dataStatus === 'CHECKING'
                ? '校验中'
                : '失败'
            }}
          </el-text>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template #default="{ row }">
          {{ row.createTime.split('T').join(' ') }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" align="center">
        <template #default="{ row }">
          {{ row.updateTime.split('T').join(' ') }}
        </template>
      </el-table-column>
      <!-- 利用作用域插槽 row 可以获取当前行的数据 => v-for 遍历 item -->
      <el-table-column label="操作" align="center">
        <template #default="{ row }">
          <el-button
            circle
            plain
            type="success"
            :icon="Download"
            @click="onDownloadArticle(row)"
            v-show="row.dataStatus === 'SUCCESS'"
          ></el-button>
          <el-button
            circle
            plain
            type="primary"
            :icon="Edit"
            @click="onEditArticle(row)"
          ></el-button>
          <el-button
            circle
            plain
            type="danger"
            :icon="Delete"
            @click="onDeleteArticle(row)"
          ></el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-empty
          v-if="!params.spaceCode"
          description="请先选择对象存储空间"
        ></el-empty>
        <el-empty v-else description="没有数据"></el-empty>
      </template>
    </el-table>

    <!-- 分页区域 -->
    <el-pagination
      v-model:current-page="params.pageNum"
      v-model:page-size="params.pagesize"
      :page-sizes="[2, 3, 5, 10]"
      :background="true"
      layout="jumper, total, sizes, prev, pager, next"
      :total="total"
      @size-change="onSizeChange"
      @current-change="onCurrentChange"
      style="margin-top: 20px; justify-content: flex-end"
    />

    <!-- 添加编辑的抽屉 -->
    <article-edit ref="articleEditRef" @success="onSuccess"></article-edit>
  </page-container>
</template>

<style lang="scss" scoped></style>
