<script setup>
import { ref } from 'vue'
import ChannelSelect from './ChannelSelect.vue'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { artPublishService, artEditService, uploadFile } from '@/api/article'

// 控制抽屉显示隐藏
const visibleDrawer = ref(false)

// 默认数据
const defaultForm = {
  dataName: '',
  dataType: '',
  dataFormat: '',
  dataContant: '',
  dataCapacity: '',
  filePath: '',
  spaceCode: ''
}

const formatList = {
  矢量数据: ['shp', 'geojson'],
  影像数据: ['tif', 'img', 'jpg'],
  三维数据: ['BIM', '建筑数据', '点云', '倾斜摄影', 'dem'],
  非矢量数据: ['csv', 'excel']
}
// 准备数据
const formModel = ref({ ...defaultForm })
const progressFlag = ref(false)
const uploadPercentage = ref(0)

const onSelectFile = async (file) => {
  const fd = new FormData()
  fd.append('dataName', formModel.value['dataName'])
  fd.append('spaceCode', formModel.value['spaceCode'])
  fd.append('type', 0)
  fd.append('file', file.raw)
  progressFlag.value = true
  const { data } = await uploadFile(fd, {
    onUploadProgress: (event) => {
      uploadPercentage.value = Math.floor((event.loaded * 100) / event.total)
    }
  })
  ElMessage.success('上传成功')
  progressFlag.value = false
  uploadPercentage.value = 0
  formModel.value.dataCapacity = file.raw.size
  formModel.value.filePath = data.data
}

// 提交
const emit = defineEmits(['success'])
const onPublish = async () => {
  // 注意：当前接口，需要的是 formData 对象
  // 将普通对象 => 转换成 => formData对象
  // 发请求
  if (formModel.value.id) {
    // 编辑操作
    await artEditService(formModel.value)
    ElMessage.success('修改成功')
    visibleDrawer.value = false
    emit('success', { name: 'edit', spaceCode: formModel.value.spaceCode })
  } else {
    // 添加操作
    await artPublishService(formModel.value)
    ElMessage.success('添加成功')
    visibleDrawer.value = false
    // 通知到父组件，添加成功了
    emit('success', { name: 'add', spaceCode: formModel.value.spaceCode })
  }
}

// 组件对外暴露一个方法 open，基于open传来的参数，区分添加还是编辑
// open({})  => 表单无需渲染，说明是添加
// open({ id, ..., ... })  => 表单需要渲染，说明是编辑
// open调用后，可以打开抽屉
const open = async (row) => {
  visibleDrawer.value = true // 显示抽屉
  if (row.id) {
    formModel.value = { ...row }
  } else {
    formModel.value = { ...defaultForm } // 基于默认的数据，重置form数据
    formModel.value.spaceCode = row.spaceCode
  }
}

defineExpose({
  open
})
</script>

<template>
  <el-drawer
    v-model="visibleDrawer"
    :title="formModel.id ? '编辑数据源' : '添加数据源'"
    direction="rtl"
    size="50%"
  >
    <el-form :model="formModel" ref="formRef" label-width="100px">
      <el-form-item label="对象存储空间" prop="spaceCode">
        <channel-select
          v-model="formModel.spaceCode"
          width="100%"
        ></channel-select>
      </el-form-item>
      <el-form-item label="数据名称" prop="dataName">
        <el-input
          v-model="formModel.dataName"
          placeholder="请输入数据名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        <el-select v-model="formModel.dataType" placeholder="请选择数据类型">
          <el-option label="矢量数据" value="矢量数据"></el-option>
          <el-option label="影像数据" value="影像数据"></el-option>
          <el-option label="三维数据" value="三维数据"></el-option>
          <el-option label="非矢量数据" value="非矢量数据"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据格式" prop="dataFormat">
        <el-select
          v-model="formModel.dataFormat"
          placeholder="请选择数据格式"
          no-data-text="请选择数据类型"
        >
          <el-option
            v-for="item in formatList[formModel.dataType]"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
          <!-- <el-option label="shp" value="shp"></el-option>
          <el-option label="geojson" value="geojson"></el-option>
          <el-option label="tif" value="tif"></el-option>
          <el-option label="img" value="img"></el-option>
          <el-option label="jpg" value="jpg"></el-option>
          <el-option label="BIM" value="BIM"></el-option>
          <el-option label="建筑数据" value="建筑数据"></el-option>
          <el-option label="点云" value="点云"></el-option>
          <el-option label="倾斜摄影" value="倾斜摄影"></el-option>
          <el-option label="dem" value="dem"></el-option>
          <el-option label="csv" value="csv"></el-option>
          <el-option label="excel" value="excel"></el-option> -->
        </el-select>
      </el-form-item>
      <el-form-item label="数据描述">
        <el-input
          v-model="formModel.dataContant"
          placeholder="请输入数据描述"
          type="textarea"
        ></el-input>
      </el-form-item>
      <el-form-item label="选择文件">
        <!-- 此处需要关闭 element-plus 的自动上传，不需要配置 action 等参数
             只需要做前端的本地预览图片即可，无需在提交前上传图标
             语法：URL.createObjectURL(...) 创建本地预览的地址，来预览
        -->
        <el-upload
          class="avatar-uploader"
          :show-file-list="false"
          :auto-upload="false"
          :on-change="onSelectFile"
        >
          <el-icon class="avatar-uploader-icon">选择文件</el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <div class="progress">
          <el-progress v-if="progressFlag" :percentage="uploadPercentage" />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          @click="onPublish()"
          type="primary"
          :disabled="!formModel.dataName || !formModel.filePath"
          >确定
        </el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<style lang="scss" scoped>
.avatar-uploader {
  :deep() {
    .avatar {
      width: 178px;
      height: 30px;
      display: block;
    }
    .el-upload {
      // border: 1px dashed var(--el-border-color);
      background-color: var(--el-color-primary);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }
    .el-upload:hover {
      // border-color: var(--el-color-primary);
      background-color: rgb(121.3, 187.1, 255);
    }
    .el-icon.avatar-uploader-icon {
      font-size: 20px;
      line-height: 20px;
      color: #fff;
      font-style: normal;
      width: 178px;
      height: 30px;
      text-align: center;
    }
  }
}

.el-progress {
  width: 720px;
}
</style>
