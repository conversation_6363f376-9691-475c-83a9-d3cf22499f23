<script setup>
import { ref } from 'vue'
import { artEditChannelService, artAddChannelService } from '@/api/article.js'
const dialogVisible = ref(false)
const formRef = ref()
const codeDisabled = ref(false)
const formModel = ref({
  spaceName: '',
  spaceCode: '',
  spaceContent: '',
  userId: ''
})
const rules = {
  cate_name: [
    { required: true, message: '请输入对象存储空间名称', trigger: 'blur' },
    {
      pattern: /^\S{1,10}$/,
      message: '分类名必须是 1-10 位的非空字符',
      trigger: 'blur'
    }
  ],
  cate_alias: [
    { required: true, message: '请输入对象存储空间编号', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9]{1,15}$/,
      message: '分类名必须是 1-15 位的字母或数字',
      trigger: 'blur'
    }
  ]
}

const emit = defineEmits(['success'])
const onSubmit = async () => {
  await formRef.value.validate()
  const isEdit = formModel.value.id
  if (isEdit) {
    await artEditChannelService(formModel.value)
    ElMessage.success('编辑成功')
  } else {
    await artAddChannelService(formModel.value)
    ElMessage.success('添加成功')
  }
  dialogVisible.value = false
  emit('success')
}

// 组件对外暴露一个方法 open，基于open传来的参数，区分添加还是编辑
// open({})  => 表单无需渲染，说明是添加
// open({ id, cate_name, ... })  => 表单需要渲染，说明是编辑
// open调用后，可以打开弹窗
const open = (row) => {
  dialogVisible.value = true
  formModel.value = { ...row } // 添加 → 重置了表单内容，编辑 → 存储了需要回显的数据
  const isEdit = formModel.value.id
  codeDisabled.value = isEdit ? true : false
}

// 向外暴露方法
defineExpose({
  open
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="formModel.id ? '编辑对象存储空间' : '添加对象存储空间'"
    width="30%"
  >
    <el-form
      ref="formRef"
      :model="formModel"
      :rules="rules"
      label-width="140px"
      label-position="left"
      style="padding-right: 30px"
    >
      <el-form-item label="对象存储空间名称" prop="spaceName">
        <el-input
          v-model="formModel.spaceName"
          placeholder="请输入对象存储空间名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="对象存储空间编号" prop="spaceCode">
        <el-input
          v-model="formModel.spaceCode"
          placeholder="请输入对象存储空间编号"
          :disabled="codeDisabled"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-form-item label="对象存储空间描述">
      <el-input
        v-model="formModel.spaceContent"
        placeholder="请输入对象存储空间描述"
        type="textarea"
      ></el-input>
    </el-form-item>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
