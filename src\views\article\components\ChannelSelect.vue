<script setup>
import { artGetChannelsService } from '@/api/article.js'
import { ref } from 'vue'
const params = ref({
  pageNum: 1, // 当前页
  pageSize: 10000, // 当前生效的每页条数
  userId: ''
})
defineProps({
  modelValue: {
    type: [Number, String]
  },
  width: {
    type: String
  }
})
const emit = defineEmits(['update:modelValue'])
const channelList = ref([])
const getChannelList = async () => {
  const res = await artGetChannelsService(params.value)
  channelList.value = res.data.data
}
getChannelList()
</script>

<template>
  <!-- label 展示给用户看的，value 收集起来提交给后台的 -->
  <el-select
    :modelValue="modelValue"
    @update:modelValue="emit('update:modelValue', $event)"
    :style="{ width }"
  >
    <el-option
      v-for="channel in channelList"
      :key="channel.id"
      :label="channel.spaceName"
      :value="channel.spaceCode"
    ></el-option>
  </el-select>
</template>
